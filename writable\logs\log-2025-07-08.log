ERROR - 2025-07-08 09:23:34 --> mysqli_sql_exception: Duplicate key name 'email' in D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 D:\xampp\htdocs\appoinment\app\Database\Migrations\2025-07-08-092131_CreateUsersTable.php(89): CodeIgniter\Database\Forge->createTable('users')
#5 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 D:\xampp\htdocs\appoinment\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-07-08 09:23:55 --> mysqli_sql_exception: Can't create table `appoinment`.`appointments` (errno: 150 "Foreign key constraint is incorrectly formed") in D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `a...', 0)
#1 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `a...')
#2 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `a...')
#3 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `a...')
#4 D:\xampp\htdocs\appoinment\app\Database\Migrations\2025-07-08-092138_CreateAppointmentsTable.php(79): CodeIgniter\Database\Forge->createTable('appointments')
#5 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateAppointmentsTable->up()
#6 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 D:\xampp\htdocs\appoinment\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-07-08 09:24:38 --> mysqli_sql_exception: Duplicate key name 'email' in D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `a...', 0)
#1 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `a...')
#2 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `a...')
#3 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `a...')
#4 D:\xampp\htdocs\appoinment\app\Database\Migrations\2025-07-08-092150_CreateAdminsTable.php(63): CodeIgniter\Database\Forge->createTable('admins')
#5 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateAdminsTable->up()
#6 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 D:\xampp\htdocs\appoinment\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-07-08 10:06:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:07:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:07:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:10:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:11:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:11:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:12:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:15:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:15:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:21:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:26:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:27:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:27:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:27:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:27:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-07-08 10:29:04 --> mysqli_sql_exception: Duplicate entry '<EMAIL>' for key 'email' in D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ad...', 0)
#1 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ad...')
#2 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ad...')
#3 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1841): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ad...', NULL, false)
#4 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2230): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 D:\xampp\htdocs\appoinment\app\Database\Seeds\AdminSeeder.php(34): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Database\Seeder.php(149): App\Database\Seeds\AdminSeeder->run()
#7 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(79): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Seed->run(Array)
#9 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 D:\xampp\htdocs\appoinment\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 D:\xampp\htdocs\appoinment\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-07-08 10:29:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:29:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:29:24 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:29:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:29:45 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:30:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:30:43 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:30:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:30:56 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:32:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:32:50 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:32:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:32:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:32:58 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-07-08 10:35:02 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: GET, Route: debug/admin]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\DebugController.php(17): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DebugController->testAdmin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DebugController))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\appoinment\\public\\index.php')
DEBUG - 2025-07-08 10:35:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:35:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:36:03 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "last_login" is not nullable, but null was passed.
[Method: POST, Route: admin/login]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'last_login', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(333): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\AdminAuthController.php(53): CodeIgniter\BaseModel->first()
 6 APPPATH\Controllers\AdminAuthController.php(25): App\Controllers\AdminAuthController->processLogin()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminAuthController->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminAuthController))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:37:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:38:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:41:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:41:12 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/users.php"
[Method: GET, Route: admin/users]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/users.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/users', [], true)
 3 APPPATH\Controllers\AdminController.php(150): view('admin/users', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminController->users()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminController))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:42:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:42:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:42:22 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "holiday_date" is not nullable, but null was passed.
[Method: GET, Route: admin/time-slots]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'holiday_date', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(291): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Controllers\AdminController.php(162): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminController->timeSlots()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminController))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:42:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:42:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:42:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-08 10:42:44 --> CodeIgniter\Exceptions\InvalidArgumentException: Field "holiday_date" is not nullable, but null was passed.
[Method: GET, Route: admin/time-slots]
in SYSTEMPATH\DataCaster\DataCaster.php on line 145.
 1 SYSTEMPATH\DataConverter\DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs(null, 'holiday_date', 'get')
 2 SYSTEMPATH\BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 3 SYSTEMPATH\Model.php(291): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 4 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH\Controllers\AdminController.php(162): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\AdminController->timeSlots()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\AdminController))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-07-08 10:42:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:42:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:43:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:44:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:45:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:45:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:45:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-07-08 10:48:03 --> Registration error: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: string, and its value: '1989-07-01'
DEBUG - 2025-07-08 10:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:52:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:52:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:52:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:53:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:54:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:54:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:55:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:56:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:56:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:57:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 10:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-08 11:03:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
