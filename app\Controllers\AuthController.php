<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;

class AuthController extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function login()
    {
        // If user is already logged in, redirect to dashboard
        if (session()->get('user_id')) {
            return redirect()->to(base_url('dashboard'));
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->processLogin();
        }

        $data = [
            'title' => 'Login - DentalCare'
        ];

        return view('auth/login', $data);
    }

    private function processLogin()
    {
        $rules = [
            'identifier' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return view('auth/login', [
                'title' => 'Login - DentalCare',
                'validation' => $this->validator
            ]);
        }

        $identifier = $this->request->getPost('identifier');
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember');

        // Find user by email or phone
        $user = $this->userModel->findByEmailOrPhone($identifier);

        if (!$user) {
            session()->setFlashdata('error', 'Invalid email/phone or password.');
            return redirect()->back()->withInput();
        }

        // Verify password
        if (!$this->userModel->verifyPassword($password, $user['password'])) {
            session()->setFlashdata('error', 'Invalid email/phone or password.');
            return redirect()->back()->withInput();
        }

        // Check if user is active
        if (!$user['is_active']) {
            session()->setFlashdata('error', 'Your account has been deactivated. Please contact support.');
            return redirect()->back()->withInput();
        }

        // Set session data
        $sessionData = [
            'user_id' => $user['id'],
            'user_name' => $user['first_name'] . ' ' . $user['last_name'],
            'user_email' => $user['email'],
            'user_phone' => $user['phone'],
            'is_logged_in' => true
        ];

        session()->set($sessionData);

        // Handle remember me
        if ($remember) {
            // Set remember me cookie (optional implementation)
        }

        session()->setFlashdata('success', 'Welcome back, ' . $user['first_name'] . '!');
        return redirect()->to(base_url('dashboard'));
    }

    public function register()
    {
        // If user is already logged in, redirect to dashboard
        if (session()->get('user_id')) {
            return redirect()->to(base_url('dashboard'));
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->processRegistration();
        }

        $data = [
            'title' => 'Register - DentalCare'
        ];

        return view('auth/register', $data);
    }

    private function processRegistration()
    {
        $rules = [
            'first_name' => 'required|min_length[2]|max_length[100]',
            'last_name' => 'required|min_length[2]|max_length[100]',
            'email' => 'permit_empty|valid_email|is_unique[users.email]',
            'phone' => 'permit_empty|min_length[10]|max_length[20]|is_unique[users.phone]',
            'password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[password]',
            'gender' => 'permit_empty|in_list[male,female,other]',
            'terms' => 'required'
        ];

        $messages = [
            'email.is_unique' => 'This email is already registered.',
            'phone.is_unique' => 'This phone number is already registered.',
            'confirm_password.matches' => 'Password confirmation does not match.',
            'terms.required' => 'You must accept the terms and conditions.'
        ];

        if (!$this->validate($rules, $messages)) {
            return view('auth/register', [
                'title' => 'Register - DentalCare',
                'validation' => $this->validator
            ]);
        }

        $email = $this->request->getPost('email');
        $phone = $this->request->getPost('phone');

        // Validate that at least email or phone is provided
        if (empty($email) && empty($phone)) {
            session()->setFlashdata('error', 'Please provide either an email address or phone number.');
            return redirect()->back()->withInput();
        }

        $userData = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'email' => $email ?: null,
            'phone' => $phone ?: null,
            'password' => $this->request->getPost('password'),
            'date_of_birth' => $this->request->getPost('date_of_birth') ?: null,
            'gender' => $this->request->getPost('gender') ?: null,
            'address' => $this->request->getPost('address') ?: null,
            'emergency_contact' => $this->request->getPost('emergency_contact') ?: null,
            'medical_history' => $this->request->getPost('medical_history') ?: null,
            'is_active' => true
        ];

        try {
            $userId = $this->userModel->insert($userData);

            if ($userId) {
                session()->setFlashdata('success', 'Account created successfully! Please login to continue.');
                return redirect()->to(base_url('auth/login'));
            } else {
                session()->setFlashdata('error', 'Failed to create account. Please try again.');
                return redirect()->back()->withInput();
            }
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'An error occurred while creating your account. Please try again.');
            return redirect()->back()->withInput();
        }
    }

    public function logout()
    {
        session()->destroy();
        session()->setFlashdata('success', 'You have been logged out successfully.');
        return redirect()->to(base_url());
    }
}
