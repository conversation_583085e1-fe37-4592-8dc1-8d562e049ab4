<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-calendar-plus text-primary" style="font-size: 3rem;"></i>
                        <h3 class="fw-bold mt-3">Book Your Appointment</h3>
                        <p class="text-muted">Choose your preferred date and time</p>
                    </div>

                    <div id="alertContainer"></div>

                    <form id="appointmentForm">
                        <?= csrf_field() ?>
                        
                        <!-- Step 1: Select Date -->
                        <div class="step-container" id="step1">
                            <h5 class="fw-bold mb-3">
                                <span class="badge bg-primary me-2">1</span>
                                Select Date
                            </h5>
                            
                            <div class="mb-4">
                                <label for="appointment_date" class="form-label">Appointment Date *</label>
                                <input type="date" 
                                       class="form-control form-control-lg" 
                                       id="appointment_date" 
                                       name="appointment_date" 
                                       min="<?= date('Y-m-d') ?>"
                                       required>
                                <div class="form-text">Select a date for your appointment</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary btn-lg" id="checkSlotsBtn">
                                    <i class="bi bi-search me-2"></i>Check Available Times
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Select Time Slot -->
                        <div class="step-container d-none" id="step2">
                            <h5 class="fw-bold mb-3">
                                <span class="badge bg-primary me-2">2</span>
                                Select Time
                            </h5>
                            
                            <div class="mb-4">
                                <label class="form-label">Available Time Slots</label>
                                <div id="timeSlotsContainer" class="row g-2">
                                    <!-- Time slots will be loaded here -->
                                </div>
                                <input type="hidden" id="time_slot_id" name="time_slot_id" required>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary" id="backToStep1">
                                    <i class="bi bi-arrow-left me-2"></i>Back
                                </button>
                                <button type="button" class="btn btn-primary flex-fill" id="continueToStep3" disabled>
                                    <i class="bi bi-arrow-right me-2"></i>Continue
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Appointment Details -->
                        <div class="step-container d-none" id="step3">
                            <h5 class="fw-bold mb-3">
                                <span class="badge bg-primary me-2">3</span>
                                Appointment Details
                            </h5>
                            
                            <div class="mb-3">
                                <label for="service_type" class="form-label">Service Type *</label>
                                <select class="form-select" id="service_type" name="service_type" required>
                                    <option value="">Select a service</option>
                                    <option value="General Checkup">General Checkup</option>
                                    <option value="Teeth Cleaning">Teeth Cleaning</option>
                                    <option value="Root Canal">Root Canal Treatment</option>
                                    <option value="Orthodontics">Orthodontics Consultation</option>
                                    <option value="Tooth Extraction">Tooth Extraction</option>
                                    <option value="Dental Filling">Dental Filling</option>
                                    <option value="Teeth Whitening">Teeth Whitening</option>
                                    <option value="Emergency">Emergency Treatment</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="notes" class="form-label">Additional Notes</label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Any specific concerns or requests (optional)"></textarea>
                                <div class="form-text">Maximum 500 characters</div>
                            </div>

                            <!-- Appointment Summary -->
                            <div class="card bg-light mb-4">
                                <div class="card-body">
                                    <h6 class="fw-bold mb-3">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        Appointment Summary
                                    </h6>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <p class="mb-1"><strong>Date:</strong></p>
                                            <p class="text-muted" id="summaryDate">-</p>
                                        </div>
                                        <div class="col-sm-6">
                                            <p class="mb-1"><strong>Time:</strong></p>
                                            <p class="text-muted" id="summaryTime">-</p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <p class="mb-1"><strong>Service:</strong></p>
                                            <p class="text-muted" id="summaryService">-</p>
                                        </div>
                                        <div class="col-sm-6">
                                            <p class="mb-1"><strong>Status:</strong></p>
                                            <span class="badge bg-warning">Pending Confirmation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary" id="backToStep2">
                                    <i class="bi bi-arrow-left me-2"></i>Back
                                </button>
                                <button type="submit" class="btn btn-success btn-lg flex-fill" id="bookAppointmentBtn">
                                    <span class="btn-text">
                                        <i class="bi bi-calendar-check me-2"></i>Book Appointment
                                    </span>
                                    <span class="loading-spinner">
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        Booking...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Working Hours Info -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="fw-bold mb-3">
                        <i class="bi bi-info-circle text-primary me-2"></i>
                        Working Hours
                    </h6>
                    <div class="row text-sm">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-1">Monday - Friday: 9:00 AM - 6:00 PM</li>
                                <li class="mb-1">Saturday: 9:00 AM - 1:00 PM</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-1">Sunday: Closed</li>
                                <li class="mb-1">Appointments every 30 minutes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let selectedSlot = null;
    let selectedDate = null;

    // Step navigation
    function showStep(stepNumber) {
        document.querySelectorAll('.step-container').forEach(step => {
            step.classList.add('d-none');
        });
        document.getElementById('step' + stepNumber).classList.remove('d-none');
    }

    // Check available slots
    document.getElementById('checkSlotsBtn').addEventListener('click', function() {
        const date = document.getElementById('appointment_date').value;
        
        if (!date) {
            showAlert('Please select a date first.', 'warning');
            return;
        }

        selectedDate = date;
        showLoading(this);
        
        fetch(`<?= base_url('appointments/get-available-slots') ?>?date=${date}`)
            .then(response => response.json())
            .then(data => {
                hideLoading(this);
                
                if (data.error) {
                    showAlert(data.error, 'danger');
                    return;
                }
                
                if (data.slots.length === 0) {
                    showAlert('No available time slots for the selected date. Please choose another date.', 'warning');
                    return;
                }
                
                displayTimeSlots(data.slots);
                showStep(2);
            })
            .catch(error => {
                hideLoading(this);
                showAlert('Error loading time slots. Please try again.', 'danger');
            });
    });

    // Display time slots
    function displayTimeSlots(slots) {
        const container = document.getElementById('timeSlotsContainer');
        container.innerHTML = '';
        
        slots.forEach(slot => {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'col-md-4 col-sm-6';
            timeSlot.innerHTML = `
                <button type="button" class="btn btn-outline-primary w-100 time-slot-btn" 
                        data-slot-id="${slot.id}" 
                        data-start-time="${slot.start_time}" 
                        data-end-time="${slot.end_time}">
                    ${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}
                </button>
            `;
            container.appendChild(timeSlot);
        });
        
        // Add click handlers to time slot buttons
        document.querySelectorAll('.time-slot-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.time-slot-btn').forEach(b => {
                    b.classList.remove('btn-primary');
                    b.classList.add('btn-outline-primary');
                });
                
                // Add active class to clicked button
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-primary');
                
                // Store selected slot
                selectedSlot = {
                    id: this.dataset.slotId,
                    startTime: this.dataset.startTime,
                    endTime: this.dataset.endTime
                };
                
                document.getElementById('time_slot_id').value = selectedSlot.id;
                document.getElementById('continueToStep3').disabled = false;
            });
        });
    }

    // Format time for display
    function formatTime(time) {
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    }

    // Navigation buttons
    document.getElementById('backToStep1').addEventListener('click', () => showStep(1));
    document.getElementById('continueToStep3').addEventListener('click', function() {
        updateSummary();
        showStep(3);
    });
    document.getElementById('backToStep2').addEventListener('click', () => showStep(2));

    // Update appointment summary
    function updateSummary() {
        const date = new Date(selectedDate);
        const formattedDate = date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        document.getElementById('summaryDate').textContent = formattedDate;
        document.getElementById('summaryTime').textContent = 
            `${formatTime(selectedSlot.startTime)} - ${formatTime(selectedSlot.endTime)}`;
    }

    // Update service in summary when changed
    document.getElementById('service_type').addEventListener('change', function() {
        document.getElementById('summaryService').textContent = this.value || '-';
    });

    // Form submission
    document.getElementById('appointmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const bookBtn = document.getElementById('bookAppointmentBtn');
        
        showLoading(bookBtn);
        
        fetch('<?= base_url('appointments/book') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(bookBtn);
            
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '<?= base_url('appointments') ?>';
                }, 2000);
            } else {
                if (data.errors) {
                    let errorMsg = 'Please fix the following errors:\n';
                    Object.values(data.errors).forEach(error => {
                        errorMsg += '- ' + error + '\n';
                    });
                    showAlert(errorMsg, 'danger');
                } else {
                    showAlert(data.message || 'Failed to book appointment.', 'danger');
                }
            }
        })
        .catch(error => {
            hideLoading(bookBtn);
            showAlert('An error occurred. Please try again.', 'danger');
        });
    });

    // Show alert function
    function showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alert);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Set minimum date to today
    document.getElementById('appointment_date').min = new Date().toISOString().split('T')[0];
</script>
<?= $this->endSection() ?>
