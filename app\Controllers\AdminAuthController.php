<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AdminModel;

class AdminAuthController extends BaseController
{
    protected $adminModel;

    public function __construct()
    {
        $this->adminModel = new AdminModel();
    }

    public function login()
    {
        // If admin is already logged in, redirect to dashboard
        if (session()->get('admin_id')) {
            return redirect()->to(base_url('admin/dashboard'));
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->processLogin();
        }

        $data = [
            'title' => 'Admin Login - DentalCare'
        ];

        return view('admin/auth/login', $data);
    }

    private function processLogin()
    {
        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return view('admin/auth/login', [
                'title' => 'Admin Login - DentalCare',
                'validation' => $this->validator
            ]);
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Find admin by email
        $admin = $this->adminModel->where('email', $email)->first();

        if (!$admin) {
            session()->setFlashdata('error', 'Invalid email or password.');
            return redirect()->back()->withInput();
        }

        // Verify password
        if (!$this->adminModel->verifyPassword($password, $admin['password'])) {
            session()->setFlashdata('error', 'Invalid email or password.');
            return redirect()->back()->withInput();
        }

        // Check if admin is active
        if (!$admin['is_active']) {
            session()->setFlashdata('error', 'Your account has been deactivated. Please contact support.');
            return redirect()->back()->withInput();
        }

        // Set session data
        $sessionData = [
            'admin_id' => $admin['id'],
            'admin_name' => $admin['name'],
            'admin_email' => $admin['email'],
            'admin_role' => $admin['role'],
            'is_admin_logged_in' => true
        ];

        session()->set($sessionData);

        // Update last login
        $this->adminModel->updateLastLogin($admin['id']);

        session()->setFlashdata('success', 'Welcome back, ' . $admin['name'] . '!');
        return redirect()->to(base_url('admin/dashboard'));
    }

    public function logout()
    {
        session()->remove(['admin_id', 'admin_name', 'admin_email', 'admin_role', 'is_admin_logged_in']);
        session()->setFlashdata('success', 'You have been logged out successfully.');
        return redirect()->to(base_url('admin/login'));
    }
}
