<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">My Appointments</h2>
                    <p class="text-muted">Manage your dental appointments</p>
                </div>
                <a href="<?= base_url('book-appointment') ?>" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Book New Appointment
                </a>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (empty($appointments)): ?>
                <!-- No appointments -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 mb-2">No Appointments Yet</h4>
                        <p class="text-muted mb-4">You haven't booked any appointments yet. Book your first appointment to get started.</p>
                        <a href="<?= base_url('book-appointment') ?>" class="btn btn-primary btn-lg">
                            <i class="bi bi-calendar-plus me-2"></i>Book Your First Appointment
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Appointments List -->
                <div class="row">
                    <?php 
                    $upcomingAppointments = [];
                    $pastAppointments = [];
                    
                    foreach ($appointments as $appointment) {
                        if (strtotime($appointment['appointment_date']) >= strtotime(date('Y-m-d'))) {
                            $upcomingAppointments[] = $appointment;
                        } else {
                            $pastAppointments[] = $appointment;
                        }
                    }
                    ?>
                    
                    <!-- Upcoming Appointments -->
                    <?php if (!empty($upcomingAppointments)): ?>
                        <div class="col-12 mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="bi bi-calendar-event text-primary me-2"></i>
                                Upcoming Appointments
                            </h5>
                            <div class="row g-3">
                                <?php foreach ($upcomingAppointments as $appointment): ?>
                                    <div class="col-lg-6">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?= esc($appointment['service_type']) ?></h6>
                                                        <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                            <?= ucfirst($appointment['status']) ?>
                                                        </span>
                                                    </div>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                type="button" data-bs-toggle="dropdown">
                                                            <i class="bi bi-three-dots"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <?php if ($appointment['status'] !== 'cancelled' && $appointment['status'] !== 'completed'): ?>
                                                                <li>
                                                                    <button class="dropdown-item text-danger" 
                                                                            onclick="cancelAppointment(<?= $appointment['id'] ?>)">
                                                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                                                    </button>
                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                </div>
                                                
                                                <div class="row text-sm">
                                                    <div class="col-6">
                                                        <p class="mb-1 text-muted">Date</p>
                                                        <p class="fw-medium"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <p class="mb-1 text-muted">Time</p>
                                                        <p class="fw-medium"><?= formatTime($appointment['start_time']) ?></p>
                                                    </div>
                                                </div>
                                                
                                                <?php if (!empty($appointment['notes'])): ?>
                                                    <div class="mt-3">
                                                        <p class="mb-1 text-muted small">Notes</p>
                                                        <p class="small"><?= esc($appointment['notes']) ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($appointment['admin_notes'])): ?>
                                                    <div class="mt-3">
                                                        <p class="mb-1 text-muted small">Admin Notes</p>
                                                        <p class="small text-info"><?= esc($appointment['admin_notes']) ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Past Appointments -->
                    <?php if (!empty($pastAppointments)): ?>
                        <div class="col-12">
                            <h5 class="fw-bold mb-3">
                                <i class="bi bi-clock-history text-secondary me-2"></i>
                                Past Appointments
                            </h5>
                            <div class="row g-3">
                                <?php foreach ($pastAppointments as $appointment): ?>
                                    <div class="col-lg-6">
                                        <div class="card h-100 opacity-75">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?= esc($appointment['service_type']) ?></h6>
                                                        <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                            <?= ucfirst($appointment['status']) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div class="row text-sm">
                                                    <div class="col-6">
                                                        <p class="mb-1 text-muted">Date</p>
                                                        <p class="fw-medium"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></p>
                                                    </div>
                                                    <div class="col-6">
                                                        <p class="mb-1 text-muted">Time</p>
                                                        <p class="fw-medium"><?= formatTime($appointment['start_time']) ?></p>
                                                    </div>
                                                </div>
                                                
                                                <?php if (!empty($appointment['notes'])): ?>
                                                    <div class="mt-3">
                                                        <p class="mb-1 text-muted small">Notes</p>
                                                        <p class="small"><?= esc($appointment['notes']) ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($appointment['admin_notes'])): ?>
                                                    <div class="mt-3">
                                                        <p class="mb-1 text-muted small">Admin Notes</p>
                                                        <p class="small text-info"><?= esc($appointment['admin_notes']) ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Cancel Appointment Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Appointment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="cancelForm" method="post">
                <div class="modal-body">
                    <p>Are you sure you want to cancel this appointment?</p>
                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Reason for cancellation (optional)</label>
                        <textarea class="form-control" id="cancellation_reason" name="reason" rows="3" 
                                  placeholder="Please let us know why you're cancelling..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Appointment</button>
                    <button type="submit" class="btn btn-danger">Cancel Appointment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function cancelAppointment(appointmentId) {
        const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
        const form = document.getElementById('cancelForm');
        form.action = `<?= base_url('appointments/cancel') ?>/${appointmentId}`;
        modal.show();
    }

    // Format time for display
    function formatTime(time) {
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    }
</script>
<?= $this->endSection() ?>

<?php
// Helper function for status colors
function getStatusColor($status) {
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'confirmed':
            return 'success';
        case 'completed':
            return 'primary';
        case 'cancelled':
            return 'danger';
        case 'rescheduled':
            return 'info';
        default:
            return 'secondary';
    }
}

// Helper function for time formatting
function formatTime($time) {
    $parts = explode(':', $time);
    $hour = (int)$parts[0];
    $minute = $parts[1];
    $ampm = $hour >= 12 ? 'PM' : 'AM';
    $displayHour = $hour % 12 ?: 12;
    return "{$displayHour}:{$minute} {$ampm}";
}
?>
