<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">Manage Appointments</h2>
                    <p class="text-muted">View and manage all patient appointments</p>
                </div>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Filter by Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?= $current_status === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="confirmed" <?= $current_status === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                                <option value="completed" <?= $current_status === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="cancelled" <?= $current_status === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                <option value="rescheduled" <?= $current_status === 'rescheduled' ? 'selected' : '' ?>>Rescheduled</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="date" class="form-label">Filter by Date</label>
                            <input type="date" class="form-select" id="date" name="date" value="<?= $current_date ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-funnel me-2"></i>Filter
                            </button>
                            <a href="<?= base_url('admin/appointments') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Appointments Table -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($appointments)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 mb-2">No Appointments Found</h4>
                            <p class="text-muted">No appointments match your current filters.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Patient</th>
                                        <th>Contact</th>
                                        <th>Service</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-medium"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></div>
                                                <div class="text-muted small"><?= formatTime($appointment['start_time']) ?></div>
                                            </td>
                                            <td>
                                                <div class="fw-medium"><?= esc($appointment['first_name'] . ' ' . $appointment['last_name']) ?></div>
                                                <div class="text-muted small"><?= esc($appointment['email']) ?></div>
                                            </td>
                                            <td>
                                                <?php if ($appointment['phone']): ?>
                                                    <a href="tel:<?= $appointment['phone'] ?>" class="text-decoration-none">
                                                        <i class="bi bi-telephone me-1"></i><?= $appointment['phone'] ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">No phone</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= esc($appointment['service_type']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                    <?= ucfirst($appointment['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($appointment['notes'])): ?>
                                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                                          title="<?= esc($appointment['notes']) ?>">
                                                        <?= esc($appointment['notes']) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                            type="button" data-bs-toggle="dropdown">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($appointment['status'] === 'pending'): ?>
                                                            <li>
                                                                <button class="dropdown-item" 
                                                                        onclick="updateStatus(<?= $appointment['id'] ?>, 'confirmed')">
                                                                    <i class="bi bi-check-circle text-success me-2"></i>Confirm
                                                                </button>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (in_array($appointment['status'], ['pending', 'confirmed'])): ?>
                                                            <li>
                                                                <button class="dropdown-item" 
                                                                        onclick="updateStatus(<?= $appointment['id'] ?>, 'completed')">
                                                                    <i class="bi bi-check-square text-primary me-2"></i>Mark Complete
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <button class="dropdown-item" 
                                                                        onclick="updateStatus(<?= $appointment['id'] ?>, 'cancelled')">
                                                                    <i class="bi bi-x-circle text-danger me-2"></i>Cancel
                                                                </button>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item" 
                                                                    onclick="addNotes(<?= $appointment['id'] ?>, '<?= esc($appointment['admin_notes'] ?? '') ?>')">
                                                                <i class="bi bi-chat-left-text text-info me-2"></i>Add Notes
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Appointment Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="appointmentId" name="appointment_id">
                    <input type="hidden" id="newStatus" name="status">
                    
                    <div class="mb-3">
                        <label for="adminNotes" class="form-label">Admin Notes (optional)</label>
                        <textarea class="form-control" id="adminNotes" name="admin_notes" rows="3" 
                                  placeholder="Add any notes about this status change..."></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="statusMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="confirmStatusBtn">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Admin Notes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="notesForm">
                <div class="modal-body">
                    <input type="hidden" id="notesAppointmentId" name="appointment_id">
                    <input type="hidden" name="status" value="">
                    
                    <div class="mb-3">
                        <label for="notesText" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="notesText" name="admin_notes" rows="4" 
                                  placeholder="Add notes about this appointment..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Notes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function updateStatus(appointmentId, status) {
        document.getElementById('appointmentId').value = appointmentId;
        document.getElementById('newStatus').value = status;
        
        const statusMessages = {
            'confirmed': 'This will confirm the appointment and notify the patient.',
            'completed': 'This will mark the appointment as completed.',
            'cancelled': 'This will cancel the appointment and notify the patient.'
        };
        
        document.getElementById('statusMessage').textContent = statusMessages[status] || 'Update appointment status.';
        
        const modal = new bootstrap.Modal(document.getElementById('statusModal'));
        modal.show();
    }
    
    function addNotes(appointmentId, currentNotes) {
        document.getElementById('notesAppointmentId').value = appointmentId;
        document.getElementById('notesText').value = currentNotes;
        
        const modal = new bootstrap.Modal(document.getElementById('notesModal'));
        modal.show();
    }
    
    // Handle status form submission
    document.getElementById('statusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const btn = document.getElementById('confirmStatusBtn');
        
        showLoading(btn);
        
        fetch('<?= base_url('admin/appointments/update-status') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(btn);
            
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading(btn);
            alert('An error occurred. Please try again.');
        });
    });
    
    // Handle notes form submission
    document.getElementById('notesForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch('<?= base_url('admin/appointments/update-status') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    });
</script>
<?= $this->endSection() ?>

<?php
// Helper function for status colors
function getStatusColor($status) {
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'confirmed':
            return 'success';
        case 'completed':
            return 'primary';
        case 'cancelled':
            return 'danger';
        case 'rescheduled':
            return 'info';
        default:
            return 'secondary';
    }
}

// Helper function for time formatting
function formatTime($time) {
    $parts = explode(':', $time);
    $hour = (int)$parts[0];
    $minute = $parts[1];
    $ampm = $hour >= 12 ? 'PM' : 'AM';
    $displayHour = $hour % 12 ?: 12;
    return "{$displayHour}:{$minute} {$ampm}";
}
?>
