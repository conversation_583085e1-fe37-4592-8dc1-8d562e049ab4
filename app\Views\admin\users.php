<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">Manage Patients</h2>
                    <p class="text-muted">View and manage patient accounts</p>
                </div>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Users Table -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 mb-2">No Patients Found</h4>
                            <p class="text-muted">No patients have registered yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Patient</th>
                                        <th>Contact</th>
                                        <th>Registration Date</th>
                                        <th>Status</th>
                                        <th>Appointments</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-medium"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                                                <?php if (!empty($user['date_of_birth'])): ?>
                                                    <div class="text-muted small">
                                                        Born: <?= date('M j, Y', strtotime($user['date_of_birth'])) ?>
                                                        <?php if (!empty($user['gender'])): ?>
                                                            • <?= ucfirst($user['gender']) ?>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['email']): ?>
                                                    <div>
                                                        <i class="bi bi-envelope me-1"></i>
                                                        <a href="mailto:<?= $user['email'] ?>" class="text-decoration-none">
                                                            <?= esc($user['email']) ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($user['phone']): ?>
                                                    <div>
                                                        <i class="bi bi-telephone me-1"></i>
                                                        <a href="tel:<?= $user['phone'] ?>" class="text-decoration-none">
                                                            <?= esc($user['phone']) ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($user['emergency_contact']): ?>
                                                    <div class="text-muted small">
                                                        Emergency: <?= esc($user['emergency_contact']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div><?= date('M j, Y', strtotime($user['created_at'])) ?></div>
                                                <div class="text-muted small"><?= date('g:i A', strtotime($user['created_at'])) ?></div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                                <?php if ($user['email_verified_at']): ?>
                                                    <div class="text-success small mt-1">
                                                        <i class="bi bi-check-circle me-1"></i>Email verified
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($user['phone_verified_at']): ?>
                                                    <div class="text-success small mt-1">
                                                        <i class="bi bi-check-circle me-1"></i>Phone verified
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('admin/appointments?user_id=' . $user['id']) ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-calendar-check me-1"></i>
                                                    View Appointments
                                                </a>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                            type="button" data-bs-toggle="dropdown">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <button class="dropdown-item" 
                                                                    onclick="viewPatientDetails(<?= htmlspecialchars(json_encode($user)) ?>)">
                                                                <i class="bi bi-eye text-primary me-2"></i>View Details
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" 
                                                               href="<?= base_url('admin/appointments?user_id=' . $user['id']) ?>">
                                                                <i class="bi bi-calendar-check text-info me-2"></i>Appointments
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item" 
                                                                    onclick="toggleUserStatus(<?= $user['id'] ?>, <?= $user['is_active'] ? 'false' : 'true' ?>)">
                                                                <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?> text-warning me-2"></i>
                                                                <?= $user['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Patient Details Modal -->
<div class="modal fade" id="patientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Patient Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Personal Information</h6>
                        <div class="mb-2">
                            <strong>Name:</strong> <span id="patientName"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Email:</strong> <span id="patientEmail"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Phone:</strong> <span id="patientPhone"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Date of Birth:</strong> <span id="patientDob"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Gender:</strong> <span id="patientGender"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Emergency Contact:</strong> <span id="patientEmergency"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Account Information</h6>
                        <div class="mb-2">
                            <strong>Registration Date:</strong> <span id="patientRegistered"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Status:</strong> <span id="patientStatus"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Email Verified:</strong> <span id="patientEmailVerified"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Phone Verified:</strong> <span id="patientPhoneVerified"></span>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold mb-3">Address</h6>
                        <p id="patientAddress" class="text-muted"></p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold mb-3">Medical History</h6>
                        <p id="patientMedicalHistory" class="text-muted"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="bookAppointmentForPatient()">
                    <i class="bi bi-calendar-plus me-2"></i>Book Appointment
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentPatient = null;

    function viewPatientDetails(patient) {
        currentPatient = patient;
        
        document.getElementById('patientName').textContent = patient.first_name + ' ' + patient.last_name;
        document.getElementById('patientEmail').textContent = patient.email || 'Not provided';
        document.getElementById('patientPhone').textContent = patient.phone || 'Not provided';
        document.getElementById('patientDob').textContent = patient.date_of_birth ? 
            new Date(patient.date_of_birth).toLocaleDateString() : 'Not provided';
        document.getElementById('patientGender').textContent = patient.gender ? 
            patient.gender.charAt(0).toUpperCase() + patient.gender.slice(1) : 'Not specified';
        document.getElementById('patientEmergency').textContent = patient.emergency_contact || 'Not provided';
        
        document.getElementById('patientRegistered').textContent = 
            new Date(patient.created_at).toLocaleDateString();
        document.getElementById('patientStatus').innerHTML = patient.is_active ? 
            '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>';
        document.getElementById('patientEmailVerified').textContent = patient.email_verified_at ? 
            'Yes (' + new Date(patient.email_verified_at).toLocaleDateString() + ')' : 'No';
        document.getElementById('patientPhoneVerified').textContent = patient.phone_verified_at ? 
            'Yes (' + new Date(patient.phone_verified_at).toLocaleDateString() + ')' : 'No';
        
        document.getElementById('patientAddress').textContent = patient.address || 'No address provided';
        document.getElementById('patientMedicalHistory').textContent = patient.medical_history || 'No medical history provided';
        
        const modal = new bootstrap.Modal(document.getElementById('patientModal'));
        modal.show();
    }

    function toggleUserStatus(userId, newStatus) {
        const action = newStatus === 'true' ? 'activate' : 'deactivate';
        if (confirm(`Are you sure you want to ${action} this patient account?`)) {
            // Implementation for toggling user status
            console.log('Toggle user status', userId, newStatus);
            // You can implement this functionality later
        }
    }

    function bookAppointmentForPatient() {
        if (currentPatient) {
            // Redirect to appointment booking with pre-filled patient info
            window.location.href = `<?= base_url('admin/appointments/book') ?>?user_id=${currentPatient.id}`;
        }
    }
</script>
<?= $this->endSection() ?>
