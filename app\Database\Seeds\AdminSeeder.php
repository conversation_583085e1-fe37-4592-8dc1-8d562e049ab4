<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AdminSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name'       => 'Admin User',
                'email'      => '<EMAIL>',
                'password'   => password_hash('admin123', PASSWORD_DEFAULT),
                'role'       => 'super_admin',
                'phone'      => '+1234567890',
                'is_active'  => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name'       => 'Receptionist',
                'email'      => '<EMAIL>',
                'password'   => password_hash('receptionist123', PASSWORD_DEFAULT),
                'role'       => 'receptionist',
                'phone'      => '+1234567891',
                'is_active'  => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('admins')->insertBatch($data);
    }
}
