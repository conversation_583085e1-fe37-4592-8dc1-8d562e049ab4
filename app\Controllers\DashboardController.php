<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AppointmentModel;

class DashboardController extends BaseController
{
    protected $appointmentModel;

    public function __construct()
    {
        $this->appointmentModel = new AppointmentModel();
    }

    public function index()
    {
        // Check if user is logged in
        if (!session()->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $userId = session()->get('user_id');

        // Get upcoming appointments
        $upcomingAppointments = $this->appointmentModel->getUpcomingAppointments($userId, 3);

        // Get appointment statistics
        $totalAppointments = $this->appointmentModel->where('user_id', $userId)->countAllResults();
        $pendingAppointments = $this->appointmentModel->where('user_id', $userId)
            ->where('status', 'pending')
            ->countAllResults();
        $completedAppointments = $this->appointmentModel->where('user_id', $userId)
            ->where('status', 'completed')
            ->countAllResults();

        $data = [
            'title' => 'Dashboard - DentalCare',
            'upcomingAppointments' => $upcomingAppointments,
            'stats' => [
                'total' => $totalAppointments,
                'pending' => $pendingAppointments,
                'completed' => $completedAppointments
            ]
        ];

        return view('dashboard/index', $data);
    }
}
