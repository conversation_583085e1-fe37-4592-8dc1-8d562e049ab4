<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">Dashboard</h2>
                    <p class="text-muted">Welcome back, <?= session()->get('admin_name') ?>!</p>
                </div>
                <div class="text-muted">
                    <i class="bi bi-calendar me-1"></i>
                    <?= date('l, F j, Y') ?>
                </div>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3">
                                    <i class="bi bi-calendar-check fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['total_appointments'] ?></h3>
                                    <p class="text-muted mb-0">Total Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3" style="background-color: #10b981;">
                                    <i class="bi bi-calendar-day fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['today_appointments'] ?></h3>
                                    <p class="text-muted mb-0">Today's Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3" style="background-color: #f59e0b;">
                                    <i class="bi bi-clock fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['pending_appointments'] ?></h3>
                                    <p class="text-muted mb-0">Pending Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3" style="background-color: #8b5cf6;">
                                    <i class="bi bi-people fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['total_users'] ?></h3>
                                    <p class="text-muted mb-0">Total Patients</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Today's Appointments -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">
                                    <i class="bi bi-calendar-day text-primary me-2"></i>
                                    Today's Appointments
                                </h5>
                                <a href="<?= base_url('admin/appointments?date=' . date('Y-m-d')) ?>" class="btn btn-sm btn-outline-primary">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($today_appointments)): ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                                    <h6 class="mt-3 mb-2">No Appointments Today</h6>
                                    <p class="text-muted">There are no appointments scheduled for today.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>Patient</th>
                                                <th>Service</th>
                                                <th>Status</th>
                                                <th>Contact</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($today_appointments as $appointment): ?>
                                                <tr>
                                                    <td class="fw-medium">
                                                        <?= formatTime($appointment['start_time']) ?>
                                                    </td>
                                                    <td>
                                                        <?= esc($appointment['first_name'] . ' ' . $appointment['last_name']) ?>
                                                    </td>
                                                    <td><?= esc($appointment['service_type']) ?></td>
                                                    <td>
                                                        <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                            <?= ucfirst($appointment['status']) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($appointment['phone']): ?>
                                                            <a href="tel:<?= $appointment['phone'] ?>" class="text-decoration-none">
                                                                <i class="bi bi-telephone me-1"></i><?= $appointment['phone'] ?>
                                                            </a>
                                                        <?php else: ?>
                                                            <span class="text-muted">No phone</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="fw-bold mb-0">
                                <i class="bi bi-lightning text-primary me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-3">
                                <a href="<?= base_url('admin/appointments') ?>" class="btn btn-primary">
                                    <i class="bi bi-calendar-check me-2"></i>Manage Appointments
                                </a>
                                <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-people me-2"></i>Manage Patients
                                </a>
                                <a href="<?= base_url('admin/time-slots') ?>" class="btn btn-outline-secondary">
                                    <i class="bi bi-clock me-2"></i>Time Slots
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card mt-4">
                        <div class="card-header bg-white">
                            <h5 class="fw-bold mb-0">
                                <i class="bi bi-activity text-primary me-2"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_appointments)): ?>
                                <p class="text-muted text-center">No recent activity</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach (array_slice($recent_appointments, 0, 5) as $appointment): ?>
                                        <div class="list-group-item px-0 py-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1 fw-medium">
                                                        <?= esc($appointment['first_name'] . ' ' . $appointment['last_name']) ?>
                                                    </h6>
                                                    <p class="mb-1 small text-muted">
                                                        <?= esc($appointment['service_type']) ?> - 
                                                        <?= date('M j, Y', strtotime($appointment['appointment_date'])) ?>
                                                    </p>
                                                </div>
                                                <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                    <?= ucfirst($appointment['status']) ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?php
// Helper function for status colors
function getStatusColor($status) {
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'confirmed':
            return 'success';
        case 'completed':
            return 'primary';
        case 'cancelled':
            return 'danger';
        case 'rescheduled':
            return 'info';
        default:
            return 'secondary';
    }
}

// Helper function for time formatting
function formatTime($time) {
    $parts = explode(':', $time);
    $hour = (int)$parts[0];
    $minute = $parts[1];
    $ampm = $hour >= 12 ? 'PM' : 'AM';
    $displayHour = $hour % 12 ?: 12;
    return "{$displayHour}:{$minute} {$ampm}";
}
?>
