<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateTimeSlotsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'day_of_week' => [
                'type'       => 'ENUM',
                'constraint' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
            ],
            'start_time' => [
                'type' => 'TIME',
            ],
            'end_time' => [
                'type' => 'TIME',
            ],
            'duration_minutes' => [
                'type'       => 'INT',
                'constraint' => 3,
                'default'    => 30,
            ],
            'max_appointments' => [
                'type'       => 'INT',
                'constraint' => 2,
                'default'    => 1,
            ],
            'is_available' => [
                'type'    => 'BOOLEAN',
                'default' => true,
            ],
            'is_holiday' => [
                'type'    => 'BOOLEAN',
                'default' => false,
            ],
            'holiday_date' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('day_of_week');
        $this->forge->addKey('start_time');
        $this->forge->addKey('is_available');
        $this->forge->addKey('holiday_date');

        $this->forge->createTable('time_slots');
    }

    public function down()
    {
        $this->forge->dropTable('time_slots');
    }
}
