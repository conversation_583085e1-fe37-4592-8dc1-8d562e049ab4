<?php

namespace App\Models;

use CodeIgniter\Model;

class AppointmentModel extends Model
{
    protected $table            = 'appointments';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'time_slot_id',
        'appointment_date',
        'appointment_time',
        'service_type',
        'status',
        'notes',
        'admin_notes',
        'cancellation_reason',
        'reminder_sent'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'reminder_sent' => 'boolean',
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'user_id' => 'required|integer',
        'time_slot_id' => 'required|integer',
        'appointment_date' => 'required|valid_date',
        'appointment_time' => 'required',
        'service_type' => 'required|max_length[100]',
        'status' => 'permit_empty|in_list[pending,confirmed,completed,cancelled,rescheduled]',
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function getUserAppointments($userId, $status = null)
    {
        $builder = $this->select('appointments.*, time_slots.day_of_week, time_slots.start_time, time_slots.end_time')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id')
            ->where('appointments.user_id', $userId)
            ->orderBy('appointments.appointment_date', 'ASC')
            ->orderBy('appointments.appointment_time', 'ASC');

        if ($status) {
            $builder->where('appointments.status', $status);
        }

        return $builder->findAll();
    }

    public function getUpcomingAppointments($userId, $limit = 5)
    {
        return $this->select('appointments.*, time_slots.day_of_week, time_slots.start_time, time_slots.end_time')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id')
            ->where('appointments.user_id', $userId)
            ->where('appointments.appointment_date >=', date('Y-m-d'))
            ->whereIn('appointments.status', ['pending', 'confirmed'])
            ->orderBy('appointments.appointment_date', 'ASC')
            ->orderBy('appointments.appointment_time', 'ASC')
            ->limit($limit)
            ->findAll();
    }

    public function isTimeSlotAvailable($timeSlotId, $date, $excludeAppointmentId = null)
    {
        $builder = $this->where('time_slot_id', $timeSlotId)
            ->where('appointment_date', $date)
            ->whereIn('status', ['pending', 'confirmed']);

        if ($excludeAppointmentId) {
            $builder->where('id !=', $excludeAppointmentId);
        }

        return $builder->countAllResults() === 0;
    }

    public function getAppointmentWithDetails($appointmentId)
    {
        return $this->select('appointments.*, users.first_name, users.last_name, users.email, users.phone,
                             time_slots.day_of_week, time_slots.start_time, time_slots.end_time')
            ->join('users', 'users.id = appointments.user_id')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id')
            ->where('appointments.id', $appointmentId)
            ->first();
    }
}
