<?php

namespace App\Models;

use CodeIgniter\Model;

class TimeSlotModel extends Model
{
    protected $table            = 'time_slots';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'day_of_week',
        'start_time',
        'end_time',
        'duration_minutes',
        'max_appointments',
        'is_available',
        'is_holiday',
        'holiday_date'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'is_available' => 'boolean',
        'is_holiday' => 'boolean',
        'holiday_date' => 'datetime',
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'day_of_week' => 'required|in_list[monday,tuesday,wednesday,thursday,friday,saturday,sunday]',
        'start_time' => 'required',
        'end_time' => 'required',
        'duration_minutes' => 'required|integer|greater_than[0]',
        'max_appointments' => 'required|integer|greater_than[0]',
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function getAvailableSlotsByDate($date)
    {
        $dayOfWeek = strtolower(date('l', strtotime($date)));

        return $this->where('day_of_week', $dayOfWeek)
            ->where('is_available', true)
            ->where('is_holiday', false)
            ->orderBy('start_time', 'ASC')
            ->findAll();
    }

    public function getAvailableSlotsForBooking($date)
    {
        $appointmentModel = new \App\Models\AppointmentModel();
        $slots = $this->getAvailableSlotsByDate($date);
        $availableSlots = [];

        foreach ($slots as $slot) {
            if ($appointmentModel->isTimeSlotAvailable($slot['id'], $date)) {
                $availableSlots[] = $slot;
            }
        }

        return $availableSlots;
    }

    public function getWorkingDays()
    {
        return $this->select('day_of_week')
            ->where('is_available', true)
            ->where('is_holiday', false)
            ->groupBy('day_of_week')
            ->orderBy("FIELD(day_of_week, 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')")
            ->findAll();
    }

    public function isWorkingDay($date)
    {
        $dayOfWeek = strtolower(date('l', strtotime($date)));

        return $this->where('day_of_week', $dayOfWeek)
            ->where('is_available', true)
            ->where('is_holiday', false)
            ->countAllResults() > 0;
    }
}
