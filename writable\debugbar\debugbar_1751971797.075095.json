{"url": "http://localhost/appoinment/index.php/appointments/get-available-slots", "method": "GET", "isAJAX": false, "startTime": **********.989446, "totalTime": 73.3, "totalMemory": "6.740", "segmentDuration": 15, "segmentCount": 5, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.993173, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.014041, "duration": 0.008573055267333984}, {"name": "Routing", "component": "Timer", "start": **********.022627, "duration": 0.0008809566497802734}, {"name": "Before Filters", "component": "Timer", "start": **********.023767, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.029222, "duration": 0.033178091049194336}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.029224, "duration": 0.007480144500732422}, {"name": "After Filters", "component": "Timer", "start": **********.062421, "duration": 1.0013580322265625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.062454, "duration": 0.00037407875061035156}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(16 total Queries, 16 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `time_slots`\n<strong>WHERE</strong> `day_of_week` = &#039;wednesday&#039;\n<strong>AND</strong> `is_available` = 1\n<strong>AND</strong> `is_holiday` = 0", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:110", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:75", "function": "        App\\Models\\TimeSlotModel->isWorkingDay()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Models\\TimeSlotModel.php:110", "qid": "c612a3bb480bd610dd80c563bf4b716f"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `time_slots`\n<strong>WHERE</strong> `day_of_week` = &#039;wednesday&#039;\n<strong>AND</strong> `is_available` = 1\n<strong>AND</strong> `is_holiday` = 0\n<strong>ORDER</strong> <strong>BY</strong> `start_time` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:75", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:81", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsByDate()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\TimeSlotModel.php:75", "qid": "567ad1714c2b3079e244613fe13dc06b"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;29&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "8e717c776474c7177f8fc652492c14c6"}, {"hover": "", "class": "", "duration": "0.13 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;30&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "d7328f73e20b332e93bb8a11871743b4"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;31&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "85be1f6347283f06a330339f26a15abc"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;32&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "9df0774084bfabe8753e5722db6bc663"}, {"hover": "", "class": "", "duration": "0.14 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;33&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "fb4f03d5edc5482642164332c5152f70"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;34&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "f1c76ee7af7ca963f0ededafb0f2b550"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;35&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "51ac3af891827c08f9dd421a168329ca"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;36&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "2c542ac3f0c998474f2064411d49604d"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;37&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "942b58cc8535e79d64c3aafc40061e9c"}, {"hover": "", "class": "", "duration": "1.18 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;38&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "ea9ac743de229ec02f880e8c13d79570"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;39&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "d0e12862eea13c2f180cf8c79b742635"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;40&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "2cd7bcd4f54065ce99b5e41f4017bb5d"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;41&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "9b1cb901f76ed13e23eb05d908837bf2"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;42&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\AppointmentModel.php:107", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Models\\TimeSlotModel.php:85", "function": "        App\\Models\\AppointmentModel->isTimeSlotAvailable()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\AppointmentController.php:79", "function": "        App\\Models\\TimeSlotModel->getAvailableSlotsForBooking()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AppointmentController->getAvailableSlots()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\AppointmentModel.php:107", "qid": "5677c3fc6fed958673ae429a0a5c2be7"}]}, "badgeValue": 16, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.042057, "duration": "0.000771"}, {"name": "Query", "component": "Database", "start": **********.043382, "duration": "0.001110", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `time_slots`\n<strong>WHERE</strong> `day_of_week` = &#039;wednesday&#039;\n<strong>AND</strong> `is_available` = 1\n<strong>AND</strong> `is_holiday` = 0"}, {"name": "Query", "component": "Database", "start": **********.045512, "duration": "0.000334", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `time_slots`\n<strong>WHERE</strong> `day_of_week` = &#039;wednesday&#039;\n<strong>AND</strong> `is_available` = 1\n<strong>AND</strong> `is_holiday` = 0\n<strong>ORDER</strong> <strong>BY</strong> `start_time` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.046688, "duration": "0.000197", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;29&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.046975, "duration": "0.000133", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;30&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.047204, "duration": "0.000120", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;31&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.047403, "duration": "0.000123", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;32&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.04761, "duration": "0.000137", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;33&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.047958, "duration": "0.000253", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;34&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.048287, "duration": "0.000166", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;35&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.048659, "duration": "0.000259", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;36&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.048994, "duration": "0.000156", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;37&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.049354, "duration": "0.001181", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;38&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.050873, "duration": "0.000343", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;39&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.051418, "duration": "0.000481", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;40&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.052101, "duration": "0.000479", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;41&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}, {"name": "Query", "component": "Database", "start": **********.052781, "duration": "0.000481", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `appointments`\n<strong>WHERE</strong> `time_slot_id` = &#039;42&#039;\n<strong>AND</strong> `appointment_date` = &#039;2025-07-09&#039;\n<strong>AND</strong> `status` <strong>IN</strong> (&#039;pending&#039;,&#039;confirmed&#039;)"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 179 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\ArrayCast.php", "name": "ArrayCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\BaseCast.php", "name": "BaseCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\BooleanCast.php", "name": "BooleanCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\CSVCast.php", "name": "CSVCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\CastInterface.php", "name": "CastInterface.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\DatetimeCast.php", "name": "DatetimeCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\FloatCast.php", "name": "FloatCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\IntBoolCast.php", "name": "IntBoolCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\IntegerCast.php", "name": "IntegerCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\JsonCast.php", "name": "JsonCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\TimestampCast.php", "name": "TimestampCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\URICast.php", "name": "URICast.php"}, {"path": "SYSTEMPATH\\DataCaster\\DataCaster.php", "name": "DataCaster.php"}, {"path": "SYSTEMPATH\\DataConverter\\DataConverter.php", "name": "DataConverter.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\Format\\Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH\\Format\\FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH\\Format\\JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Format.php", "name": "Format.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\AppointmentController.php", "name": "AppointmentController.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Models\\AppointmentModel.php", "name": "AppointmentModel.php"}, {"path": "APPPATH\\Models\\TimeSlotModel.php", "name": "TimeSlotModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "FCPATH\\vendor\\autoload.php", "name": "autoload.php"}, {"path": "FCPATH\\vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH\\vendor\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH\\vendor\\composer\\installed.php", "name": "installed.php"}, {"path": "FCPATH\\vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 179, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\AppointmentController", "method": "getAvailableSlots", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "debug/admin", "handler": "\\App\\Controllers\\DebugController::testAdmin"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\DashboardController::index"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\ProfileController::index"}, {"method": "GET", "route": "appointments", "handler": "\\App\\Controllers\\AppointmentController::index"}, {"method": "GET", "route": "book-appointment", "handler": "\\App\\Controllers\\AppointmentController::book"}, {"method": "GET", "route": "appointments/get-available-slots", "handler": "\\App\\Controllers\\AppointmentController::getAvailableSlots"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\AdminAuthController::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\AdminAuthController::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\AdminController::dashboard"}, {"method": "GET", "route": "admin/appointments", "handler": "\\App\\Controllers\\AdminController::appointments"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\AdminController::users"}, {"method": "GET", "route": "admin/time-slots", "handler": "\\App\\Controllers\\AdminController::timeSlots"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "POST", "route": "book-appointment", "handler": "\\App\\Controllers\\AppointmentController::store"}, {"method": "POST", "route": "appointments/cancel/([0-9]+)", "handler": "\\App\\Controllers\\AppointmentController::cancel/$1"}, {"method": "POST", "route": "admin/login", "handler": "\\App\\Controllers\\AdminAuthController::login"}, {"method": "POST", "route": "admin/appointments/update-status", "handler": "\\App\\Controllers\\AdminController::updateAppointmentStatus"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "11.30", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.15", "count": 16}}}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.002601, "duration": 0.011304140090942383}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.044498, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.045849, "duration": 1.3828277587890625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.046888, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.04711, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.047326, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.047528, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.047749, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.048213, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.048455, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.048919, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.049152, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.050537, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.051217, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.0519, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.052582, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.053264, "duration": 7.152557373046875e-06}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1751971683</pre>", "_ci_previous_url": "http://localhost/appoinment/index.php/book-appointment", "admin_id": "1", "admin_name": "Admin User", "admin_email": "<EMAIL>", "admin_role": "super_admin", "is_admin_logged_in": "<pre>1</pre>", "user_id": "1", "user_name": "<PERSON><PERSON>", "user_email": "<EMAIL>", "user_phone": "9457790679", "is_logged_in": "<pre>1</pre>"}, "get": {"date": "2025-07-09"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost/appoinment/book-appointment", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=33c9938923e9436b85020e713fd84d1c; ci_session=7c39448f0634eb3134a0a311015c491a"}, "cookies": {"csrf_cookie_name": "33c9938923e9436b85020e713fd84d1c", "ci_session": "7c39448f0634eb3134a0a311015c491a"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Content-Type": "application/json; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/appoinment/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}