<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Debug routes
$routes->get('debug/admin', 'DebugController::testAdmin');
$routes->post('debug/test-post', 'DebugController::testPost');

// Authentication routes
$routes->group('auth', function ($routes) {
    $routes->get('login', 'AuthController::login');
    $routes->post('login', 'AuthController::login');
    $routes->get('register', 'AuthController::register');
    $routes->post('register', 'AuthController::register');
    $routes->get('logout', 'AuthController::logout');
});

// Dashboard routes (protected)
$routes->group('', ['filter' => 'auth'], function ($routes) {
    $routes->get('dashboard', 'DashboardController::index');
    $routes->get('profile', 'ProfileController::index');
    $routes->get('appointments', 'AppointmentController::index');
    $routes->get('book-appointment', 'AppointmentController::book');
    $routes->post('book-appointment', 'AppointmentController::store');
    $routes->post('appointments/book', 'AppointmentController::store');
    $routes->get('appointments/get-available-slots', 'AppointmentController::getAvailableSlots');
    $routes->post('appointments/cancel/(:num)', 'AppointmentController::cancel/$1');
});

// Admin routes (protected)
$routes->group('admin', function ($routes) {
    $routes->get('login', 'AdminAuthController::login');
    $routes->post('login', 'AdminAuthController::login');
    $routes->get('logout', 'AdminAuthController::logout');

    // Protected admin routes
    $routes->group('', ['filter' => 'admin'], function ($routes) {
        $routes->get('dashboard', 'AdminController::dashboard');
        $routes->get('appointments', 'AdminController::appointments');
        $routes->post('appointments/update-status', 'AdminController::updateAppointmentStatus');
        $routes->get('users', 'AdminController::users');
        $routes->get('time-slots', 'AdminController::timeSlots');
    });
});
