{"url": "http://localhost/appoinment/index.php/admin/time-slots", "method": "GET", "isAJAX": false, "startTime": **********.574697, "totalTime": 69.7, "totalMemory": "7.741", "segmentDuration": 10, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.578427, "duration": 0.018594980239868164}, {"name": "Required Before Filters", "component": "Timer", "start": **********.597023, "duration": 0.0023560523986816406}, {"name": "Routing", "component": "Timer", "start": **********.599386, "duration": 0.*****************}, {"name": "Before Filters", "component": "Timer", "start": **********.600149, "duration": 0.006245136260986328}, {"name": "Controller", "component": "Timer", "start": **********.6064, "duration": 0.037297964096069336}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.606403, "duration": 0.008622884750366211}, {"name": "After Filters", "component": "Timer", "start": **********.643711, "duration": 1.0013580322265625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.643754, "duration": 0.000698089599609375}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `time_slots`\n<strong>ORDER</strong> <strong>BY</strong> `day_of_week`, `start_time`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\AdminController.php:162", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\AdminController->timeSlots()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\AdminController.php:162", "qid": "6bdce4a48851ebad9f46a53afff36489"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.618378, "duration": "0.001419"}, {"name": "Query", "component": "Database", "start": **********.620356, "duration": "0.000602", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `time_slots`\n<strong>ORDER</strong> <strong>BY</strong> `day_of_week`, `start_time`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/layouts/main.php", "component": "Views", "start": **********.64276, "duration": 0.0006389617919921875}, {"name": "View: admin/time-slots.php", "component": "Views", "start": **********.64038, "duration": 0.0032510757446289062}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 178 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\ArrayCast.php", "name": "ArrayCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\BaseCast.php", "name": "BaseCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\BooleanCast.php", "name": "BooleanCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\CSVCast.php", "name": "CSVCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\CastInterface.php", "name": "CastInterface.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\DatetimeCast.php", "name": "DatetimeCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\FloatCast.php", "name": "FloatCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\IntBoolCast.php", "name": "IntBoolCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\IntegerCast.php", "name": "IntegerCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\JsonCast.php", "name": "JsonCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\TimestampCast.php", "name": "TimestampCast.php"}, {"path": "SYSTEMPATH\\DataCaster\\Cast\\URICast.php", "name": "URICast.php"}, {"path": "SYSTEMPATH\\DataCaster\\DataCaster.php", "name": "DataCaster.php"}, {"path": "SYSTEMPATH\\DataConverter\\DataConverter.php", "name": "DataConverter.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\AdminController.php", "name": "AdminController.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\AdminFilter.php", "name": "AdminFilter.php"}, {"path": "APPPATH\\Models\\AppointmentModel.php", "name": "AppointmentModel.php"}, {"path": "APPPATH\\Models\\TimeSlotModel.php", "name": "TimeSlotModel.php"}, {"path": "APPPATH\\Models\\UserModel.php", "name": "UserModel.php"}, {"path": "APPPATH\\Views\\admin\\layouts\\main.php", "name": "main.php"}, {"path": "APPPATH\\Views\\admin\\time-slots.php", "name": "time-slots.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "FCPATH\\vendor\\autoload.php", "name": "autoload.php"}, {"path": "FCPATH\\vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH\\vendor\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH\\vendor\\composer\\installed.php", "name": "installed.php"}, {"path": "FCPATH\\vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 178, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\AdminController", "method": "timeSlots", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "debug/admin", "handler": "\\App\\Controllers\\DebugController::testAdmin"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\DashboardController::index"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\ProfileController::index"}, {"method": "GET", "route": "appointments", "handler": "\\App\\Controllers\\AppointmentController::index"}, {"method": "GET", "route": "book-appointment", "handler": "\\App\\Controllers\\AppointmentController::book"}, {"method": "GET", "route": "appointments/get-available-slots", "handler": "\\App\\Controllers\\AppointmentController::getAvailableSlots"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\AdminAuthController::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\AdminAuthController::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\AdminController::dashboard"}, {"method": "GET", "route": "admin/appointments", "handler": "\\App\\Controllers\\AdminController::appointments"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\AdminController::users"}, {"method": "GET", "route": "admin/time-slots", "handler": "\\App\\Controllers\\AdminController::timeSlots"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "POST", "route": "book-appointment", "handler": "\\App\\Controllers\\AppointmentController::store"}, {"method": "POST", "route": "appointments/cancel/([0-9]+)", "handler": "\\App\\Controllers\\AppointmentController::cancel/$1"}, {"method": "POST", "route": "admin/login", "handler": "\\App\\Controllers\\AdminAuthController::login"}, {"method": "POST", "route": "admin/appointments/update-status", "handler": "\\App\\Controllers\\AdminController::updateAppointmentStatus"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "7.96", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.02", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.589062, "duration": 0.007955074310302734}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.620963, "duration": 2.09808349609375e-05}]}], "vars": {"varData": {"View Data": {"title": "Manage Time Slots - <PERSON><PERSON>", "timeSlots": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (78)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (78)</li><li>Contents (78)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>day_of_week</th><th>start_time</th><th>end_time</th><th>duration_minutes</th><th>max_appointments</th><th>is_available</th><th>is_holiday</th><th>holiday_date</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>5</th><td title=\"string (1)\">6</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>6</th><td title=\"string (1)\">7</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">14:00:00</td><td title=\"string (8)\">14:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>7</th><td title=\"string (1)\">8</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">14:30:00</td><td title=\"string (8)\">15:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>8</th><td title=\"string (1)\">9</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">15:00:00</td><td title=\"string (8)\">15:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>9</th><td title=\"string (2)\">10</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">15:30:00</td><td title=\"string (8)\">16:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>10</th><td title=\"string (2)\">11</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">16:00:00</td><td title=\"string (8)\">16:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>11</th><td title=\"string (2)\">12</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">16:30:00</td><td title=\"string (8)\">17:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>12</th><td title=\"string (2)\">13</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">17:00:00</td><td title=\"string (8)\">17:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>13</th><td title=\"string (2)\">14</td><td title=\"string (6)\">monday</td><td title=\"string (8)\">17:30:00</td><td title=\"string (8)\">18:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>14</th><td title=\"string (2)\">15</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>15</th><td title=\"string (2)\">16</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>16</th><td title=\"string (2)\">17</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>17</th><td title=\"string (2)\">18</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>18</th><td title=\"string (2)\">19</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>19</th><td title=\"string (2)\">20</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>20</th><td title=\"string (2)\">21</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">14:00:00</td><td title=\"string (8)\">14:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>21</th><td title=\"string (2)\">22</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">14:30:00</td><td title=\"string (8)\">15:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>22</th><td title=\"string (2)\">23</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">15:00:00</td><td title=\"string (8)\">15:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>23</th><td title=\"string (2)\">24</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">15:30:00</td><td title=\"string (8)\">16:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>24</th><td title=\"string (2)\">25</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">16:00:00</td><td title=\"string (8)\">16:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>25</th><td title=\"string (2)\">26</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">16:30:00</td><td title=\"string (8)\">17:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>26</th><td title=\"string (2)\">27</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">17:00:00</td><td title=\"string (8)\">17:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>27</th><td title=\"string (2)\">28</td><td title=\"string (7)\">tuesday</td><td title=\"string (8)\">17:30:00</td><td title=\"string (8)\">18:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>28</th><td title=\"string (2)\">29</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>29</th><td title=\"string (2)\">30</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>30</th><td title=\"string (2)\">31</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>31</th><td title=\"string (2)\">32</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>32</th><td title=\"string (2)\">33</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>33</th><td title=\"string (2)\">34</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>34</th><td title=\"string (2)\">35</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">14:00:00</td><td title=\"string (8)\">14:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>35</th><td title=\"string (2)\">36</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">14:30:00</td><td title=\"string (8)\">15:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>36</th><td title=\"string (2)\">37</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">15:00:00</td><td title=\"string (8)\">15:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>37</th><td title=\"string (2)\">38</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">15:30:00</td><td title=\"string (8)\">16:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>38</th><td title=\"string (2)\">39</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">16:00:00</td><td title=\"string (8)\">16:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>39</th><td title=\"string (2)\">40</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">16:30:00</td><td title=\"string (8)\">17:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>40</th><td title=\"string (2)\">41</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">17:00:00</td><td title=\"string (8)\">17:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>41</th><td title=\"string (2)\">42</td><td title=\"string (9)\">wednesday</td><td title=\"string (8)\">17:30:00</td><td title=\"string (8)\">18:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>42</th><td title=\"string (2)\">43</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>43</th><td title=\"string (2)\">44</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>44</th><td title=\"string (2)\">45</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>45</th><td title=\"string (2)\">46</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>46</th><td title=\"string (2)\">47</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>47</th><td title=\"string (2)\">48</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>48</th><td title=\"string (2)\">49</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">14:00:00</td><td title=\"string (8)\">14:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>49</th><td title=\"string (2)\">50</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">14:30:00</td><td title=\"string (8)\">15:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>50</th><td title=\"string (2)\">51</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">15:00:00</td><td title=\"string (8)\">15:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>51</th><td title=\"string (2)\">52</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">15:30:00</td><td title=\"string (8)\">16:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>52</th><td title=\"string (2)\">53</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">16:00:00</td><td title=\"string (8)\">16:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>53</th><td title=\"string (2)\">54</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">16:30:00</td><td title=\"string (8)\">17:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>54</th><td title=\"string (2)\">55</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">17:00:00</td><td title=\"string (8)\">17:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>55</th><td title=\"string (2)\">56</td><td title=\"string (8)\">thursday</td><td title=\"string (8)\">17:30:00</td><td title=\"string (8)\">18:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>56</th><td title=\"string (2)\">57</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>57</th><td title=\"string (2)\">58</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>58</th><td title=\"string (2)\">59</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>59</th><td title=\"string (2)\">60</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>60</th><td title=\"string (2)\">61</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>61</th><td title=\"string (2)\">62</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>62</th><td title=\"string (2)\">63</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">14:00:00</td><td title=\"string (8)\">14:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>63</th><td title=\"string (2)\">64</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">14:30:00</td><td title=\"string (8)\">15:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>64</th><td title=\"string (2)\">65</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">15:00:00</td><td title=\"string (8)\">15:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>65</th><td title=\"string (2)\">66</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">15:30:00</td><td title=\"string (8)\">16:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>66</th><td title=\"string (2)\">67</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">16:00:00</td><td title=\"string (8)\">16:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>67</th><td title=\"string (2)\">68</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">16:30:00</td><td title=\"string (8)\">17:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>68</th><td title=\"string (2)\">69</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">17:00:00</td><td title=\"string (8)\">17:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>69</th><td title=\"string (2)\">70</td><td title=\"string (6)\">friday</td><td title=\"string (8)\">17:30:00</td><td title=\"string (8)\">18:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>70</th><td title=\"string (2)\">71</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">09:00:00</td><td title=\"string (8)\">09:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>71</th><td title=\"string (2)\">72</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">09:30:00</td><td title=\"string (8)\">10:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>72</th><td title=\"string (2)\">73</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">10:00:00</td><td title=\"string (8)\">10:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>73</th><td title=\"string (2)\">74</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">10:30:00</td><td title=\"string (8)\">11:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>74</th><td title=\"string (2)\">75</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">11:00:00</td><td title=\"string (8)\">11:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>75</th><td title=\"string (2)\">76</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">11:30:00</td><td title=\"string (8)\">12:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>76</th><td title=\"string (2)\">77</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">12:00:00</td><td title=\"string (8)\">12:30:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr><tr><th>77</th><td title=\"string (2)\">78</td><td title=\"string (8)\">saturday</td><td title=\"string (8)\">12:30:00</td><td title=\"string (8)\">13:00:00</td><td title=\"string (2)\">30</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 09:26:17</td><td title=\"string (19)\">2025-07-08 09:26:17</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[0]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[0]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[0]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[0]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[0]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[0]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[1]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[1]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[1]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[1]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[1]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[1]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[2]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[2]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[2]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[2]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[2]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[2]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[3]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[3]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[3]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[3]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[3]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[4]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[4]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[4]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[4]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[4]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[4]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[5]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[5]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[5]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[5]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[5]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[5]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[6]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:00:00\"<div class=\"access-path\">$value[6]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[6]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[6]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[6]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[6]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[7]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[7]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[7]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[7]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[7]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[7]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[8]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[8]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[8]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[8]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[8]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[8]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[8]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[9]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[9]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[9]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[9]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[9]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[9]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[10]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[10]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[10]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[10]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[10]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[10]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[10]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[11]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[11]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[11]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[11]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[11]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[11]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[11]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[11]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[12]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[12]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[12]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[12]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[12]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[12]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[12]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[12]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[12]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"monday\"<div class=\"access-path\">$value[13]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[13]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"18:00:00\"<div class=\"access-path\">$value[13]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[13]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[13]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[13]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[13]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[13]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[13]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[14]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[14]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[14]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[14]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[14]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[14]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[14]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[14]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[14]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[15]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[15]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[15]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[15]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[15]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[15]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[15]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[15]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[15]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[15]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[16]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[16]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[16]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[16]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[16]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[16]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[16]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[16]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[16]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[16]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[16]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[17]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[17]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[17]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[17]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[17]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[17]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[17]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[17]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[17]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[17]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[17]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[18]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[18]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[18]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[18]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[18]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[18]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[18]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[18]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[18]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[18]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[18]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[19]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[19]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[19]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[19]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[19]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[19]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[19]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[19]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[19]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[19]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[19]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[20]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[20]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:00:00\"<div class=\"access-path\">$value[20]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[20]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[20]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[20]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[20]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[20]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[20]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[20]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[20]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[21]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[21]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[21]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[21]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[21]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[21]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[21]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[21]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[21]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[21]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[21]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[22]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[22]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[22]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[22]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[22]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[22]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[22]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[22]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[22]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[22]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[22]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[23]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[23]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[23]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[23]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[23]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[23]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[23]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[23]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[23]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[23]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[23]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[24]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[24]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[24]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[24]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[24]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[24]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[24]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[24]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[24]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[24]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[24]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[25]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[25]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[25]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[25]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[25]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[25]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[25]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[25]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[25]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[25]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[25]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[26]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[26]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[26]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[26]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[26]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[26]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[26]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[26]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[26]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[26]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[26]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[27]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (7) \"tuesday\"<div class=\"access-path\">$value[27]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[27]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"18:00:00\"<div class=\"access-path\">$value[27]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[27]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[27]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[27]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[27]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[27]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[27]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[27]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>28</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[28]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[28]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[28]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[28]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[28]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[28]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[28]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[28]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[28]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[28]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[28]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[28]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>29</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[29]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[29]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[29]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[29]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[29]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[29]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[29]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[29]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[29]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[29]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[29]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[29]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>30</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[30]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[30]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[30]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[30]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[30]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[30]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[30]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[30]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[30]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[30]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[30]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[30]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>31</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[31]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[31]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[31]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[31]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[31]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[31]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[31]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[31]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[31]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[31]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[31]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[31]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>32</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[32]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[32]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[32]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[32]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[32]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[32]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[32]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[32]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[32]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[32]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[32]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[32]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>33</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[33]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[33]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[33]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[33]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[33]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[33]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[33]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[33]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[33]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[33]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[33]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[33]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>34</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[34]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"35\"<div class=\"access-path\">$value[34]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[34]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:00:00\"<div class=\"access-path\">$value[34]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[34]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[34]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[34]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[34]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[34]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[34]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[34]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[34]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>35</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[35]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"36\"<div class=\"access-path\">$value[35]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[35]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[35]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[35]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[35]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[35]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[35]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[35]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[35]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[35]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[35]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>36</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[36]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"37\"<div class=\"access-path\">$value[36]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[36]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[36]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[36]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[36]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[36]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[36]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[36]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[36]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[36]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[36]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>37</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[37]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"38\"<div class=\"access-path\">$value[37]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[37]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[37]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[37]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[37]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[37]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[37]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[37]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[37]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[37]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[37]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>38</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[38]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"39\"<div class=\"access-path\">$value[38]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[38]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[38]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[38]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[38]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[38]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[38]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[38]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[38]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[38]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[38]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>39</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[39]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"40\"<div class=\"access-path\">$value[39]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[39]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[39]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[39]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[39]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[39]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[39]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[39]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[39]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[39]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[39]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>40</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[40]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"41\"<div class=\"access-path\">$value[40]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[40]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[40]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[40]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[40]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[40]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[40]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[40]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[40]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[40]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[40]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>41</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[41]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"42\"<div class=\"access-path\">$value[41]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (9) \"wednesday\"<div class=\"access-path\">$value[41]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[41]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"18:00:00\"<div class=\"access-path\">$value[41]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[41]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[41]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[41]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[41]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[41]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[41]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[41]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>42</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[42]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"43\"<div class=\"access-path\">$value[42]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[42]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[42]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[42]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[42]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[42]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[42]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[42]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[42]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[42]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[42]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>43</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[43]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"44\"<div class=\"access-path\">$value[43]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[43]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[43]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[43]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[43]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[43]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[43]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[43]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[43]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[43]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[43]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>44</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[44]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"45\"<div class=\"access-path\">$value[44]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[44]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[44]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[44]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[44]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[44]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[44]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[44]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[44]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[44]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[44]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>45</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[45]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"46\"<div class=\"access-path\">$value[45]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[45]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[45]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[45]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[45]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[45]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[45]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[45]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[45]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[45]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[45]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>46</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[46]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"47\"<div class=\"access-path\">$value[46]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[46]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[46]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[46]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[46]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[46]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[46]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[46]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[46]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[46]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[46]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>47</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[47]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"48\"<div class=\"access-path\">$value[47]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[47]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[47]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[47]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[47]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[47]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[47]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[47]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[47]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[47]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[47]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>48</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[48]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"49\"<div class=\"access-path\">$value[48]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[48]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:00:00\"<div class=\"access-path\">$value[48]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[48]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[48]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[48]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[48]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[48]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[48]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[48]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[48]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>49</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[49]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"50\"<div class=\"access-path\">$value[49]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[49]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[49]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[49]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[49]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[49]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[49]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[49]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[49]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[49]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[49]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>50</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[50]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"51\"<div class=\"access-path\">$value[50]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[50]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[50]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[50]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[50]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[50]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[50]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[50]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[50]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[50]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[50]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>51</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[51]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"52\"<div class=\"access-path\">$value[51]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[51]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[51]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[51]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[51]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[51]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[51]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[51]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[51]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[51]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[51]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>52</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[52]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"53\"<div class=\"access-path\">$value[52]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[52]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[52]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[52]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[52]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[52]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[52]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[52]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[52]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[52]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[52]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>53</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[53]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"54\"<div class=\"access-path\">$value[53]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[53]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[53]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[53]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[53]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[53]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[53]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[53]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[53]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[53]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[53]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>54</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[54]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"55\"<div class=\"access-path\">$value[54]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[54]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[54]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[54]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[54]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[54]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[54]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[54]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[54]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[54]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[54]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>55</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[55]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"56\"<div class=\"access-path\">$value[55]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"thursday\"<div class=\"access-path\">$value[55]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[55]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"18:00:00\"<div class=\"access-path\">$value[55]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[55]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[55]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[55]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[55]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[55]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[55]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[55]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>56</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[56]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"57\"<div class=\"access-path\">$value[56]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[56]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[56]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[56]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[56]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[56]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[56]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[56]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[56]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[56]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[56]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>57</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[57]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[57]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[57]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[57]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[57]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[57]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[57]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[57]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[57]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[57]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[57]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[57]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>58</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[58]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"59\"<div class=\"access-path\">$value[58]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[58]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[58]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[58]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[58]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[58]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[58]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[58]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[58]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[58]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[58]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>59</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[59]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"60\"<div class=\"access-path\">$value[59]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[59]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[59]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[59]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[59]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[59]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[59]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[59]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[59]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[59]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[59]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>60</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[60]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[60]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[60]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[60]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[60]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[60]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[60]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[60]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[60]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[60]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[60]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[60]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>61</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[61]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"62\"<div class=\"access-path\">$value[61]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[61]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[61]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[61]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[61]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[61]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[61]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[61]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[61]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[61]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[61]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>62</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[62]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"63\"<div class=\"access-path\">$value[62]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[62]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:00:00\"<div class=\"access-path\">$value[62]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[62]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[62]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[62]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[62]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[62]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[62]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[62]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[62]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>63</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[63]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"64\"<div class=\"access-path\">$value[63]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[63]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"14:30:00\"<div class=\"access-path\">$value[63]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[63]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[63]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[63]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[63]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[63]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[63]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[63]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[63]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>64</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[64]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"65\"<div class=\"access-path\">$value[64]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[64]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:00:00\"<div class=\"access-path\">$value[64]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[64]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[64]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[64]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[64]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[64]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[64]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[64]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[64]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>65</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[65]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"66\"<div class=\"access-path\">$value[65]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[65]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"15:30:00\"<div class=\"access-path\">$value[65]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[65]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[65]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[65]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[65]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[65]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[65]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[65]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[65]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>66</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[66]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"67\"<div class=\"access-path\">$value[66]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[66]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:00:00\"<div class=\"access-path\">$value[66]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[66]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[66]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[66]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[66]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[66]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[66]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[66]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[66]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>67</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[67]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"68\"<div class=\"access-path\">$value[67]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[67]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"16:30:00\"<div class=\"access-path\">$value[67]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[67]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[67]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[67]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[67]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[67]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[67]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[67]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[67]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>68</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[68]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"69\"<div class=\"access-path\">$value[68]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[68]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:00:00\"<div class=\"access-path\">$value[68]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[68]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[68]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[68]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[68]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[68]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[68]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[68]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[68]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>69</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[69]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"70\"<div class=\"access-path\">$value[69]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (6) \"friday\"<div class=\"access-path\">$value[69]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"17:30:00\"<div class=\"access-path\">$value[69]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"18:00:00\"<div class=\"access-path\">$value[69]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[69]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[69]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[69]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[69]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[69]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[69]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[69]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>70</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[70]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"71\"<div class=\"access-path\">$value[70]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[70]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:00:00\"<div class=\"access-path\">$value[70]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[70]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[70]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[70]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[70]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[70]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[70]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[70]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[70]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>71</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[71]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"72\"<div class=\"access-path\">$value[71]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[71]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"09:30:00\"<div class=\"access-path\">$value[71]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[71]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[71]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[71]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[71]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[71]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[71]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[71]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[71]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>72</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[72]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"73\"<div class=\"access-path\">$value[72]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[72]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:00:00\"<div class=\"access-path\">$value[72]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[72]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[72]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[72]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[72]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[72]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[72]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[72]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[72]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>73</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[73]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"74\"<div class=\"access-path\">$value[73]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[73]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"10:30:00\"<div class=\"access-path\">$value[73]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[73]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[73]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[73]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[73]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[73]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[73]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[73]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[73]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>74</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[74]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"75\"<div class=\"access-path\">$value[74]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[74]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:00:00\"<div class=\"access-path\">$value[74]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[74]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[74]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[74]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[74]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[74]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[74]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[74]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[74]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>75</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[75]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"76\"<div class=\"access-path\">$value[75]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[75]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"11:30:00\"<div class=\"access-path\">$value[75]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[75]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[75]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[75]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[75]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[75]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[75]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[75]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[75]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>76</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[76]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"77\"<div class=\"access-path\">$value[76]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[76]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"12:00:00\"<div class=\"access-path\">$value[76]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"12:30:00\"<div class=\"access-path\">$value[76]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[76]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[76]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[76]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[76]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[76]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[76]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[76]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>77</dfn> =&gt; <var>array</var> (11)<div class=\"access-path\">$value[77]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"78\"<div class=\"access-path\">$value[77]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>day_of_week</dfn> =&gt; <var>string</var> (8) \"saturday\"<div class=\"access-path\">$value[77]['day_of_week']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>string</var> (8) \"12:30:00\"<div class=\"access-path\">$value[77]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (8) \"13:00:00\"<div class=\"access-path\">$value[77]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>duration_minutes</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[77]['duration_minutes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_appointments</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[77]['max_appointments']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_available</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[77]['is_available']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_holiday</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value[77]['is_holiday']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>holiday_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[77]['holiday_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[77]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 09:26:17\"<div class=\"access-path\">$value[77]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1751971374</pre>", "_ci_previous_url": "http://localhost/appoinment/index.php/admin/time-slots", "admin_id": "1", "admin_name": "Admin User", "admin_email": "<EMAIL>", "admin_role": "super_admin", "is_admin_logged_in": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/appoinment/admin/users", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=33c9938923e9436b85020e713fd84d1c; ci_session=7f7dc716385801daa4eff287bd3ffe9e"}, "cookies": {"csrf_cookie_name": "33c9938923e9436b85020e713fd84d1c", "ci_session": "7f7dc716385801daa4eff287bd3ffe9e"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/appoinment/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}