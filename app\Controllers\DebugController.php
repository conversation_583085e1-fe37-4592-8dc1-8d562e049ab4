<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AdminModel;

class DebugController extends BaseController
{
    public function testAdmin()
    {
        $adminModel = new AdminModel();

        echo "<h2>Admin Debug Test</h2>";

        // Check if admin exists
        $admin = $adminModel->where('email', '<EMAIL>')->first();

        if (!$admin) {
            echo "<p style='color: red;'>❌ Admin not found in database</p>";

            // Try to create admin manually
            echo "<p>Creating admin user...</p>";
            $adminData = [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'super_admin',
                'phone' => '+1234567890',
                'is_active' => true,
            ];

            try {
                $result = $adminModel->insert($adminData);
                if ($result) {
                    echo "<p style='color: green;'>✅ Admin created successfully with ID: " . $result . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to create admin</p>";
                    echo "<pre>" . print_r($adminModel->errors(), true) . "</pre>";
                }
            } catch (\Exception $e) {
                echo "<p style='color: red;'>❌ Error creating admin: " . $e->getMessage() . "</p>";
            }

            // Try to find admin again
            $admin = $adminModel->where('email', '<EMAIL>')->first();
        }

        if ($admin) {
            echo "<p style='color: green;'>✅ Admin found in database</p>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
            echo "<li><strong>Name:</strong> " . $admin['name'] . "</li>";
            echo "<li><strong>Email:</strong> " . $admin['email'] . "</li>";
            echo "<li><strong>Role:</strong> " . $admin['role'] . "</li>";
            echo "<li><strong>Is Active:</strong> " . ($admin['is_active'] ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>Last Login:</strong> " . ($admin['last_login'] ?? 'Never') . "</li>";
            echo "</ul>";

            // Test password verification
            $testPassword = 'admin123';
            $isValid = password_verify($testPassword, $admin['password']);

            if ($isValid) {
                echo "<p style='color: green;'>✅ Password verification successful for 'admin123'</p>";
            } else {
                echo "<p style='color: red;'>❌ Password verification failed for 'admin123'</p>";
                echo "<p>Stored hash: " . substr($admin['password'], 0, 50) . "...</p>";
            }

            // Test model method
            $isValidModel = $adminModel->verifyPassword($testPassword, $admin['password']);
            echo "<p>Model password verification: " . ($isValidModel ? '✅ Valid' : '❌ Invalid') . "</p>";

            // Test login simulation
            echo "<h3>Login Simulation Test</h3>";

            if ($isValid && $admin['is_active']) {
                echo "<p style='color: green;'>✅ Login would succeed</p>";

                // Test session setting
                $sessionData = [
                    'admin_id' => $admin['id'],
                    'admin_name' => $admin['name'],
                    'admin_email' => $admin['email'],
                    'admin_role' => $admin['role'],
                    'is_admin_logged_in' => true
                ];

                session()->set($sessionData);
                echo "<p style='color: green;'>✅ Session data set</p>";

                // Test updateLastLogin
                try {
                    $updateResult = $adminModel->updateLastLogin($admin['id']);
                    if ($updateResult) {
                        echo "<p style='color: green;'>✅ Last login updated successfully</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Last login update returned false</p>";
                    }
                } catch (\Exception $e) {
                    echo "<p style='color: red;'>❌ Last login update failed: " . $e->getMessage() . "</p>";
                }

                echo "<p><a href='" . base_url('admin/dashboard') . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Dashboard</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Login would fail</p>";
                if (!$isValid) echo "<p>- Password verification failed</p>";
                if (!$admin['is_active']) echo "<p>- Admin account is not active</p>";
            }
        }

        echo "<hr>";
        echo "<p><a href='" . base_url('admin/login') . "'>Go to Admin Login Page</a></p>";
        echo "<p><a href='" . base_url() . "'>Go to Home Page</a></p>";
    }

    public function testPost()
    {
        echo "<h2>POST Test</h2>";
        echo "<p>POST request received successfully!</p>";
        echo "<p>User ID from session: " . (session()->get('user_id') ?? 'Not logged in') . "</p>";
        echo "<p>Request data:</p>";
        echo "<pre>" . print_r($this->request->getPost(), true) . "</pre>";

        return $this->response->setJSON([
            'success' => true,
            'message' => 'POST test successful',
            'user_id' => session()->get('user_id'),
            'data' => $this->request->getPost()
        ]);
    }
}
