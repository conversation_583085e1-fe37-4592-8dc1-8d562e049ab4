<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-person-plus-fill text-primary" style="font-size: 3rem;"></i>
                        <h3 class="fw-bold mt-3">Create Your Account</h3>
                        <p class="text-muted">Join us and book your dental appointment</p>
                    </div>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('auth/register') ?>" method="post" id="registerForm">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" 
                                       class="form-control <?= isset($validation) && $validation->hasError('first_name') ? 'is-invalid' : '' ?>" 
                                       id="first_name" 
                                       name="first_name" 
                                       placeholder="Enter your first name"
                                       value="<?= old('first_name') ?>"
                                       required>
                                <?php if (isset($validation) && $validation->hasError('first_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('first_name') ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" 
                                       class="form-control <?= isset($validation) && $validation->hasError('last_name') ? 'is-invalid' : '' ?>" 
                                       id="last_name" 
                                       name="last_name" 
                                       placeholder="Enter your last name"
                                       value="<?= old('last_name') ?>"
                                       required>
                                <?php if (isset($validation) && $validation->hasError('last_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('last_name') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           class="form-control <?= isset($validation) && $validation->hasError('email') ? 'is-invalid' : '' ?>" 
                                           id="email" 
                                           name="email" 
                                           placeholder="Enter your email"
                                           value="<?= old('email') ?>">
                                    <?php if (isset($validation) && $validation->hasError('email')): ?>
                                        <div class="invalid-feedback">
                                            <?= $validation->getError('email') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">We'll use this for appointment confirmations</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-telephone"></i>
                                    </span>
                                    <input type="tel" 
                                           class="form-control <?= isset($validation) && $validation->hasError('phone') ? 'is-invalid' : '' ?>" 
                                           id="phone" 
                                           name="phone" 
                                           placeholder="Enter your phone number"
                                           value="<?= old('phone') ?>">
                                    <?php if (isset($validation) && $validation->hasError('phone')): ?>
                                        <div class="invalid-feedback">
                                            <?= $validation->getError('phone') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">We'll use this for appointment reminders</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> You must provide either an email address or phone number (or both) to register.
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control <?= isset($validation) && $validation->hasError('password') ? 'is-invalid' : '' ?>" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Create a password"
                                           required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                    <?php if (isset($validation) && $validation->hasError('password')): ?>
                                        <div class="invalid-feedback">
                                            <?= $validation->getError('password') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">Minimum 6 characters</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control <?= isset($validation) && $validation->hasError('confirm_password') ? 'is-invalid' : '' ?>" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           placeholder="Confirm your password"
                                           required>
                                    <?php if (isset($validation) && $validation->hasError('confirm_password')): ?>
                                        <div class="invalid-feedback">
                                            <?= $validation->getError('confirm_password') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">Date of Birth</label>
                                <input type="date" 
                                       class="form-control <?= isset($validation) && $validation->hasError('date_of_birth') ? 'is-invalid' : '' ?>" 
                                       id="date_of_birth" 
                                       name="date_of_birth" 
                                       value="<?= old('date_of_birth') ?>">
                                <?php if (isset($validation) && $validation->hasError('date_of_birth')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('date_of_birth') ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-select <?= isset($validation) && $validation->hasError('gender') ? 'is-invalid' : '' ?>" 
                                        id="gender" 
                                        name="gender">
                                    <option value="">Select gender</option>
                                    <option value="male" <?= old('gender') === 'male' ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= old('gender') === 'female' ? 'selected' : '' ?>>Female</option>
                                    <option value="other" <?= old('gender') === 'other' ? 'selected' : '' ?>>Other</option>
                                </select>
                                <?php if (isset($validation) && $validation->hasError('gender')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('gender') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control <?= isset($validation) && $validation->hasError('address') ? 'is-invalid' : '' ?>" 
                                      id="address" 
                                      name="address" 
                                      rows="2" 
                                      placeholder="Enter your address"><?= old('address') ?></textarea>
                            <?php if (isset($validation) && $validation->hasError('address')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('address') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact" class="form-label">Emergency Contact</label>
                            <input type="tel" 
                                   class="form-control <?= isset($validation) && $validation->hasError('emergency_contact') ? 'is-invalid' : '' ?>" 
                                   id="emergency_contact" 
                                   name="emergency_contact" 
                                   placeholder="Emergency contact number"
                                   value="<?= old('emergency_contact') ?>">
                            <?php if (isset($validation) && $validation->hasError('emergency_contact')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('emergency_contact') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-4">
                            <label for="medical_history" class="form-label">Medical History</label>
                            <textarea class="form-control <?= isset($validation) && $validation->hasError('medical_history') ? 'is-invalid' : '' ?>" 
                                      id="medical_history" 
                                      name="medical_history" 
                                      rows="3" 
                                      placeholder="Any allergies, medications, or medical conditions we should know about"><?= old('medical_history') ?></textarea>
                            <?php if (isset($validation) && $validation->hasError('medical_history')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('medical_history') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                                <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" id="registerBtn">
                                <span class="btn-text">
                                    <i class="bi bi-person-plus me-2"></i>Create Account
                                </span>
                                <span class="loading-spinner">
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    Creating account...
                                </span>
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">Already have an account?</p>
                            <a href="<?= base_url('auth/login') ?>" class="btn btn-outline-primary mt-2">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (password.type === 'password') {
            password.type = 'text';
            toggleIcon.classList.remove('bi-eye');
            toggleIcon.classList.add('bi-eye-slash');
        } else {
            password.type = 'password';
            toggleIcon.classList.remove('bi-eye-slash');
            toggleIcon.classList.add('bi-eye');
        }
    });

    // Form submission with loading state
    document.getElementById('registerForm').addEventListener('submit', function() {
        const registerBtn = document.getElementById('registerBtn');
        showLoading(registerBtn);
    });

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Email/Phone validation
    function validateEmailOrPhone() {
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        
        if (!email && !phone) {
            document.getElementById('email').setCustomValidity('Please provide either email or phone number');
            document.getElementById('phone').setCustomValidity('Please provide either email or phone number');
        } else {
            document.getElementById('email').setCustomValidity('');
            document.getElementById('phone').setCustomValidity('');
        }
    }

    document.getElementById('email').addEventListener('input', validateEmailOrPhone);
    document.getElementById('phone').addEventListener('input', validateEmailOrPhone);
</script>
<?= $this->endSection() ?>
