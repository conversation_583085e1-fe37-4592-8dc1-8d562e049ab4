<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AppointmentModel;
use App\Models\UserModel;
use App\Models\TimeSlotModel;

class AdminController extends BaseController
{
    protected $appointmentModel;
    protected $userModel;
    protected $timeSlotModel;

    public function __construct()
    {
        $this->appointmentModel = new AppointmentModel();
        $this->userModel = new UserModel();
        $this->timeSlotModel = new TimeSlotModel();
    }

    public function dashboard()
    {
        // Check if admin is logged in
        if (!session()->get('admin_id')) {
            return redirect()->to(base_url('admin/login'));
        }

        // Get statistics
        $totalAppointments = $this->appointmentModel->countAllResults();
        $todayAppointments = $this->appointmentModel->where('appointment_date', date('Y-m-d'))->countAllResults();
        $pendingAppointments = $this->appointmentModel->where('status', 'pending')->countAllResults();
        $totalUsers = $this->userModel->countAllResults();

        // Get recent appointments
        $recentAppointments = $this->appointmentModel
            ->select('appointments.*, users.first_name, users.last_name, users.email, users.phone')
            ->join('users', 'users.id = appointments.user_id')
            ->orderBy('appointments.created_at', 'DESC')
            ->limit(10)
            ->findAll();

        // Get today's appointments
        $todayAppointmentsList = $this->appointmentModel
            ->select('appointments.*, users.first_name, users.last_name, users.phone, time_slots.start_time, time_slots.end_time')
            ->join('users', 'users.id = appointments.user_id')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id')
            ->where('appointments.appointment_date', date('Y-m-d'))
            ->orderBy('time_slots.start_time', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Admin Dashboard - DentalCare',
            'stats' => [
                'total_appointments' => $totalAppointments,
                'today_appointments' => $todayAppointments,
                'pending_appointments' => $pendingAppointments,
                'total_users' => $totalUsers
            ],
            'recent_appointments' => $recentAppointments,
            'today_appointments' => $todayAppointmentsList
        ];

        return view('admin/dashboard', $data);
    }

    public function appointments()
    {
        // Check if admin is logged in
        if (!session()->get('admin_id')) {
            return redirect()->to(base_url('admin/login'));
        }

        $status = $this->request->getGet('status');
        $date = $this->request->getGet('date');

        $builder = $this->appointmentModel
            ->select('appointments.*, users.first_name, users.last_name, users.email, users.phone, time_slots.start_time, time_slots.end_time')
            ->join('users', 'users.id = appointments.user_id')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id');

        if ($status) {
            $builder->where('appointments.status', $status);
        }

        if ($date) {
            $builder->where('appointments.appointment_date', $date);
        }

        $appointments = $builder->orderBy('appointments.appointment_date', 'DESC')
            ->orderBy('time_slots.start_time', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Manage Appointments - Admin',
            'appointments' => $appointments,
            'current_status' => $status,
            'current_date' => $date
        ];

        return view('admin/appointments', $data);
    }

    public function updateAppointmentStatus()
    {
        if (!session()->get('admin_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $appointmentId = $this->request->getPost('appointment_id');
        $status = $this->request->getPost('status');
        $adminNotes = $this->request->getPost('admin_notes');

        $updateData = [
            'status' => $status
        ];

        if ($adminNotes) {
            $updateData['admin_notes'] = $adminNotes;
        }

        if ($this->appointmentModel->update($appointmentId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Appointment status updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update appointment status'
            ]);
        }
    }

    public function users()
    {
        // Check if admin is logged in
        if (!session()->get('admin_id')) {
            return redirect()->to(base_url('admin/login'));
        }

        $users = $this->userModel->orderBy('created_at', 'DESC')->findAll();

        $data = [
            'title' => 'Manage Users - Admin',
            'users' => $users
        ];

        return view('admin/users', $data);
    }

    public function timeSlots()
    {
        // Check if admin is logged in
        if (!session()->get('admin_id')) {
            return redirect()->to(base_url('admin/login'));
        }

        $timeSlots = $this->timeSlotModel->orderBy('day_of_week')
            ->orderBy('start_time')
            ->findAll();

        $data = [
            'title' => 'Manage Time Slots - Admin',
            'timeSlots' => $timeSlots
        ];

        return view('admin/time-slots', $data);
    }

    public function getSlotsForDate()
    {
        $date = $this->request->getGet('date');

        if (!$date) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Date is required'
            ]);
        }

        // Validate date format
        if (!strtotime($date)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid date format'
            ]);
        }

        // Get day of week for the date
        $dayOfWeek = strtolower(date('l', strtotime($date)));

        // Get time slots for this day
        $timeSlots = $this->timeSlotModel
            ->where('day_of_week', $dayOfWeek)
            ->orderBy('start_time')
            ->findAll();

        // Get appointments for this date to check which slots are booked
        $db = \Config\Database::connect();
        $appointments = $db->table('appointments')
            ->select('appointments.*, users.first_name, users.last_name, time_slots.start_time, time_slots.end_time')
            ->join('users', 'users.id = appointments.user_id')
            ->join('time_slots', 'time_slots.id = appointments.time_slot_id')
            ->where('appointments.appointment_date', $date)
            ->whereIn('appointments.status', ['pending', 'confirmed'])
            ->get()
            ->getResultArray();

        // Create a map of booked slots
        $bookedSlots = [];
        foreach ($appointments as $appointment) {
            $bookedSlots[$appointment['time_slot_id']] = [
                'patient_name' => $appointment['first_name'] . ' ' . $appointment['last_name'],
                'service_type' => $appointment['service_type'],
                'status' => $appointment['status']
            ];
        }

        // Enhance time slots with booking information
        $enhancedSlots = [];
        foreach ($timeSlots as $slot) {
            $slotData = $slot;
            $slotData['is_booked'] = isset($bookedSlots[$slot['id']]);

            if ($slotData['is_booked']) {
                $slotData['patient_name'] = $bookedSlots[$slot['id']]['patient_name'];
                $slotData['service_type'] = $bookedSlots[$slot['id']]['service_type'];
                $slotData['appointment_status'] = $bookedSlots[$slot['id']]['status'];
            }

            $enhancedSlots[] = $slotData;
        }

        return $this->response->setJSON([
            'success' => true,
            'slots' => $enhancedSlots,
            'date' => $date,
            'day_of_week' => $dayOfWeek
        ]);
    }
}
