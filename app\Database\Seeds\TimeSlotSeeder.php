<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TimeSlotSeeder extends Seeder
{
    public function run()
    {
        $data = [];

        // Working days: Monday to Friday
        $workingDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        // Morning slots: 9:00 AM to 12:00 PM (30-minute slots)
        $morningSlots = [
            '09:00:00',
            '09:30:00',
            '10:00:00',
            '10:30:00',
            '11:00:00',
            '11:30:00'
        ];

        // Afternoon slots: 2:00 PM to 6:00 PM (30-minute slots)
        $afternoonSlots = [
            '14:00:00',
            '14:30:00',
            '15:00:00',
            '15:30:00',
            '16:00:00',
            '16:30:00',
            '17:00:00',
            '17:30:00'
        ];

        $allSlots = array_merge($morningSlots, $afternoonSlots);

        foreach ($workingDays as $day) {
            foreach ($allSlots as $startTime) {
                // Calculate end time (30 minutes later)
                $endTime = date('H:i:s', strtotime($startTime . ' +30 minutes'));

                $data[] = [
                    'day_of_week'       => $day,
                    'start_time'        => $startTime,
                    'end_time'          => $endTime,
                    'duration_minutes'  => 30,
                    'max_appointments'  => 1,
                    'is_available'      => true,
                    'is_holiday'        => false,
                    'created_at'        => date('Y-m-d H:i:s'),
                    'updated_at'        => date('Y-m-d H:i:s'),
                ];
            }
        }

        // Saturday morning slots only: 9:00 AM to 1:00 PM
        $saturdaySlots = [
            '09:00:00',
            '09:30:00',
            '10:00:00',
            '10:30:00',
            '11:00:00',
            '11:30:00',
            '12:00:00',
            '12:30:00'
        ];

        foreach ($saturdaySlots as $startTime) {
            $endTime = date('H:i:s', strtotime($startTime . ' +30 minutes'));

            $data[] = [
                'day_of_week'       => 'saturday',
                'start_time'        => $startTime,
                'end_time'          => $endTime,
                'duration_minutes'  => 30,
                'max_appointments'  => 1,
                'is_available'      => true,
                'is_holiday'        => false,
                'created_at'        => date('Y-m-d H:i:s'),
                'updated_at'        => date('Y-m-d H:i:s'),
            ];
        }

        $this->db->table('time_slots')->insertBatch($data);
    }
}
