<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">Welcome back, <?= session()->get('user_name') ?>!</h2>
                    <p class="text-muted">Here's an overview of your dental appointments</p>
                </div>
                <a href="<?= base_url('book-appointment') ?>" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Book Appointment
                </a>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3">
                                    <i class="bi bi-calendar-check fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['total'] ?></h3>
                                    <p class="text-muted mb-0">Total Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3" style="background-color: #f59e0b;">
                                    <i class="bi bi-clock fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['pending'] ?></h3>
                                    <p class="text-muted mb-0">Pending Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon me-3" style="background-color: #10b981;">
                                    <i class="bi bi-check-circle fs-4"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold mb-0"><?= $stats['completed'] ?></h3>
                                    <p class="text-muted mb-0">Completed Appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Upcoming Appointments -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">
                                    <i class="bi bi-calendar-event text-primary me-2"></i>
                                    Upcoming Appointments
                                </h5>
                                <a href="<?= base_url('appointments') ?>" class="btn btn-sm btn-outline-primary">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($upcomingAppointments)): ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                                    <h6 class="mt-3 mb-2">No Upcoming Appointments</h6>
                                    <p class="text-muted mb-3">You don't have any upcoming appointments scheduled.</p>
                                    <a href="<?= base_url('book-appointment') ?>" class="btn btn-primary">
                                        <i class="bi bi-calendar-plus me-2"></i>Book Appointment
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($upcomingAppointments as $appointment): ?>
                                        <div class="list-group-item px-0">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <h6 class="fw-bold mb-0 me-3"><?= esc($appointment['service_type']) ?></h6>
                                                        <span class="badge bg-<?= getStatusColor($appointment['status']) ?>">
                                                            <?= ucfirst($appointment['status']) ?>
                                                        </span>
                                                    </div>
                                                    <div class="row text-sm">
                                                        <div class="col-sm-6">
                                                            <p class="mb-1 text-muted">
                                                                <i class="bi bi-calendar me-1"></i>
                                                                <?= date('l, M j, Y', strtotime($appointment['appointment_date'])) ?>
                                                            </p>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <p class="mb-1 text-muted">
                                                                <i class="bi bi-clock me-1"></i>
                                                                <?= formatTime($appointment['start_time']) ?>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <?php if (!empty($appointment['notes'])): ?>
                                                        <p class="small text-muted mb-0 mt-2">
                                                            <i class="bi bi-chat-left-text me-1"></i>
                                                            <?= esc($appointment['notes']) ?>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="ms-3">
                                                    <?php 
                                                    $daysUntil = ceil((strtotime($appointment['appointment_date']) - time()) / (60 * 60 * 24));
                                                    ?>
                                                    <?php if ($daysUntil == 0): ?>
                                                        <span class="badge bg-danger">Today</span>
                                                    <?php elseif ($daysUntil == 1): ?>
                                                        <span class="badge bg-warning">Tomorrow</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info"><?= $daysUntil ?> days</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="fw-bold mb-0">
                                <i class="bi bi-lightning text-primary me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-3">
                                <a href="<?= base_url('book-appointment') ?>" class="btn btn-primary">
                                    <i class="bi bi-calendar-plus me-2"></i>Book New Appointment
                                </a>
                                <a href="<?= base_url('appointments') ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-calendar-check me-2"></i>View All Appointments
                                </a>
                                <a href="<?= base_url('profile') ?>" class="btn btn-outline-secondary">
                                    <i class="bi bi-person me-2"></i>Update Profile
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="card mt-4">
                        <div class="card-header bg-white">
                            <h5 class="fw-bold mb-0">
                                <i class="bi bi-telephone text-primary me-2"></i>
                                Contact Us
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <p class="mb-1 text-muted small">Phone</p>
                                <p class="fw-medium">
                                    <a href="tel:+15551234567" class="text-decoration-none">
                                        +****************
                                    </a>
                                </p>
                            </div>
                            <div class="mb-3">
                                <p class="mb-1 text-muted small">Email</p>
                                <p class="fw-medium">
                                    <a href="mailto:<EMAIL>" class="text-decoration-none">
                                        <EMAIL>
                                    </a>
                                </p>
                            </div>
                            <div class="mb-0">
                                <p class="mb-1 text-muted small">Emergency</p>
                                <p class="fw-medium text-danger">
                                    <a href="tel:+15551234999" class="text-decoration-none text-danger">
                                        +****************
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?php
// Helper function for status colors
function getStatusColor($status) {
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'confirmed':
            return 'success';
        case 'completed':
            return 'primary';
        case 'cancelled':
            return 'danger';
        case 'rescheduled':
            return 'info';
        default:
            return 'secondary';
    }
}

// Helper function for time formatting
function formatTime($time) {
    $parts = explode(':', $time);
    $hour = (int)$parts[0];
    $minute = $parts[1];
    $ampm = $hour >= 12 ? 'PM' : 'AM';
    $displayHour = $hour % 12 ?: 12;
    return "{$displayHour}:{$minute} {$ampm}";
}
?>
