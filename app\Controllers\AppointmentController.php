<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AppointmentModel;
use App\Models\TimeSlotModel;

class AppointmentController extends BaseController
{
    protected $appointmentModel;
    protected $timeSlotModel;

    public function __construct()
    {
        $this->appointmentModel = new AppointmentModel();
        $this->timeSlotModel = new TimeSlotModel();
    }

    public function index()
    {
        // Check if user is logged in
        if (!session()->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $userId = session()->get('user_id');
        $appointments = $this->appointmentModel->getUserAppointments($userId);

        $data = [
            'title' => 'My Appointments - DentalCare',
            'appointments' => $appointments
        ];

        return view('appointments/index', $data);
    }

    public function book()
    {
        // Check if user is logged in
        if (!session()->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->store();
        }

        $data = [
            'title' => 'Book Appointment - DentalCare'
        ];

        return view('appointments/book', $data);
    }

    public function getAvailableSlots()
    {
        $date = $this->request->getGet('date');

        if (!$date) {
            return $this->response->setJSON(['error' => 'Date is required']);
        }

        // Validate date format
        if (!strtotime($date)) {
            return $this->response->setJSON(['error' => 'Invalid date format']);
        }

        // Check if date is in the past
        if (strtotime($date) < strtotime(date('Y-m-d'))) {
            return $this->response->setJSON(['error' => 'Cannot book appointments for past dates']);
        }

        // Check if it's a working day
        if (!$this->timeSlotModel->isWorkingDay($date)) {
            return $this->response->setJSON(['error' => 'Selected date is not a working day']);
        }

        $slots = $this->timeSlotModel->getAvailableSlotsForBooking($date);

        return $this->response->setJSON(['slots' => $slots]);
    }

    public function store()
    {
        $rules = [
            'appointment_date' => 'required|valid_date',
            'time_slot_id' => 'required|integer',
            'service_type' => 'required|max_length[100]',
            'notes' => 'permit_empty|max_length[500]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $userId = session()->get('user_id');
        $appointmentDate = $this->request->getPost('appointment_date');
        $timeSlotId = $this->request->getPost('time_slot_id');
        $serviceType = $this->request->getPost('service_type');
        $notes = $this->request->getPost('notes');

        // Validate date is not in the past
        if (strtotime($appointmentDate) < strtotime(date('Y-m-d'))) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot book appointments for past dates'
            ]);
        }

        // Check if time slot is available
        if (!$this->appointmentModel->isTimeSlotAvailable($timeSlotId, $appointmentDate)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Selected time slot is no longer available'
            ]);
        }

        // Get time slot details
        $timeSlot = $this->timeSlotModel->find($timeSlotId);
        if (!$timeSlot) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid time slot selected'
            ]);
        }

        $appointmentData = [
            'user_id' => $userId,
            'time_slot_id' => $timeSlotId,
            'appointment_date' => $appointmentDate,
            'appointment_time' => $timeSlot['start_time'],
            'service_type' => $serviceType,
            'status' => 'pending',
            'notes' => $notes,
            'reminder_sent' => false
        ];

        try {
            $appointmentId = $this->appointmentModel->insert($appointmentData);

            if ($appointmentId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Appointment booked successfully!',
                    'appointment_id' => $appointmentId
                ]);
            } else {
                // Get validation errors from the model
                $errors = $this->appointmentModel->errors();
                $errorMessage = 'Failed to book appointment.';
                if (!empty($errors)) {
                    $errorMessage .= ' Errors: ' . implode(', ', $errors);
                }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => $errorMessage
                ]);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            log_message('error', 'Appointment booking error: ' . $e->getMessage());

            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while booking your appointment: ' . $e->getMessage()
            ]);
        }
    }

    public function cancel($id)
    {
        if (!session()->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $userId = session()->get('user_id');
        $appointment = $this->appointmentModel->where('id', $id)
            ->where('user_id', $userId)
            ->first();

        if (!$appointment) {
            session()->setFlashdata('error', 'Appointment not found.');
            return redirect()->to(base_url('appointments'));
        }

        if ($appointment['status'] === 'cancelled') {
            session()->setFlashdata('error', 'Appointment is already cancelled.');
            return redirect()->to(base_url('appointments'));
        }

        if ($appointment['status'] === 'completed') {
            session()->setFlashdata('error', 'Cannot cancel completed appointment.');
            return redirect()->to(base_url('appointments'));
        }

        $updateData = [
            'status' => 'cancelled',
            'cancellation_reason' => $this->request->getPost('reason') ?: 'Cancelled by patient'
        ];

        if ($this->appointmentModel->update($id, $updateData)) {
            session()->setFlashdata('success', 'Appointment cancelled successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to cancel appointment.');
        }

        return redirect()->to(base_url('appointments'));
    }
}
