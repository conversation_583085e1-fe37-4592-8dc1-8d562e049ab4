<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">Time Slot Management</h2>
                    <p class="text-muted">Configure available appointment time slots</p>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSlotModal">
                    <i class="bi bi-plus-circle me-2"></i>Add Time Slot
                </button>
            </div>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Time Slots by Day -->
            <?php 
            $dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            $groupedSlots = [];
            
            foreach ($timeSlots as $slot) {
                $groupedSlots[$slot['day_of_week']][] = $slot;
            }
            ?>

            <div class="row">
                <?php foreach ($dayOrder as $day): ?>
                    <?php if (isset($groupedSlots[$day])): ?>
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-white">
                                    <h5 class="fw-bold mb-0 text-capitalize">
                                        <i class="bi bi-calendar-day text-primary me-2"></i>
                                        <?= ucfirst($day) ?>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-2">
                                        <?php foreach ($groupedSlots[$day] as $slot): ?>
                                            <div class="col-md-6">
                                                <div class="card border <?= $slot['is_available'] ? 'border-success' : 'border-danger' ?>">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <div>
                                                                <h6 class="fw-bold mb-1">
                                                                    <?= formatTime($slot['start_time']) ?> - <?= formatTime($slot['end_time']) ?>
                                                                </h6>
                                                                <small class="text-muted">
                                                                    <?= $slot['duration_minutes'] ?> minutes
                                                                </small>
                                                            </div>
                                                            <div class="dropdown">
                                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                        type="button" data-bs-toggle="dropdown">
                                                                    <i class="bi bi-three-dots"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li>
                                                                        <button class="dropdown-item" 
                                                                                onclick="editSlot(<?= htmlspecialchars(json_encode($slot)) ?>)">
                                                                            <i class="bi bi-pencil text-primary me-2"></i>Edit
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item" 
                                                                                onclick="toggleSlot(<?= $slot['id'] ?>, <?= $slot['is_available'] ? 'false' : 'true' ?>)">
                                                                            <i class="bi bi-<?= $slot['is_available'] ? 'pause' : 'play' ?> text-warning me-2"></i>
                                                                            <?= $slot['is_available'] ? 'Disable' : 'Enable' ?>
                                                                        </button>
                                                                    </li>
                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <li>
                                                                        <button class="dropdown-item text-danger" 
                                                                                onclick="deleteSlot(<?= $slot['id'] ?>)">
                                                                            <i class="bi bi-trash me-2"></i>Delete
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="badge bg-<?= $slot['is_available'] ? 'success' : 'danger' ?>">
                                                                <?= $slot['is_available'] ? 'Available' : 'Disabled' ?>
                                                            </span>
                                                            <small class="text-muted">
                                                                Max: <?= $slot['max_appointments'] ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>

            <!-- Working Hours Summary -->
            <div class="card mt-4">
                <div class="card-header bg-white">
                    <h5 class="fw-bold mb-0">
                        <i class="bi bi-clock text-primary me-2"></i>
                        Working Hours Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($dayOrder as $day): ?>
                            <?php if (isset($groupedSlots[$day])): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <h6 class="fw-bold text-capitalize"><?= ucfirst($day) ?></h6>
                                    <?php 
                                    $availableSlots = array_filter($groupedSlots[$day], function($slot) {
                                        return $slot['is_available'];
                                    });
                                    
                                    if (empty($availableSlots)): ?>
                                        <p class="text-muted mb-0">Closed</p>
                                    <?php else: 
                                        $times = array_map(function($slot) {
                                            return $slot['start_time'];
                                        }, $availableSlots);
                                        sort($times);
                                        $firstTime = reset($times);
                                        $lastTime = end($times);
                                        
                                        // Calculate end time of last slot
                                        $lastSlot = array_filter($availableSlots, function($slot) use ($lastTime) {
                                            return $slot['start_time'] === $lastTime;
                                        });
                                        $lastSlot = reset($lastSlot);
                                        $endTime = $lastSlot['end_time'];
                                    ?>
                                        <p class="mb-0">
                                            <?= formatTime($firstTime) ?> - <?= formatTime($endTime) ?>
                                        </p>
                                        <small class="text-muted"><?= count($availableSlots) ?> slots available</small>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <h6 class="fw-bold text-capitalize"><?= ucfirst($day) ?></h6>
                                    <p class="text-muted mb-0">Closed</p>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Time Slot Modal -->
<div class="modal fade" id="addSlotModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Time Slot</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addSlotForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="day_of_week" class="form-label">Day of Week</label>
                            <select class="form-select" id="day_of_week" name="day_of_week" required>
                                <option value="">Select day</option>
                                <option value="monday">Monday</option>
                                <option value="tuesday">Tuesday</option>
                                <option value="wednesday">Wednesday</option>
                                <option value="thursday">Thursday</option>
                                <option value="friday">Friday</option>
                                <option value="saturday">Saturday</option>
                                <option value="sunday">Sunday</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                            <select class="form-select" id="duration_minutes" name="duration_minutes" required>
                                <option value="15">15 minutes</option>
                                <option value="30" selected>30 minutes</option>
                                <option value="45">45 minutes</option>
                                <option value="60">60 minutes</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_time" class="form-label">Start Time</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_time" class="form-label">End Time</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" readonly>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_appointments" class="form-label">Max Appointments</label>
                            <input type="number" class="form-control" id="max_appointments" name="max_appointments" 
                                   value="1" min="1" max="10" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available" checked>
                                <label class="form-check-label" for="is_available">
                                    Available for booking
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Time Slot</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Time Slot Modal -->
<div class="modal fade" id="editSlotModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Time Slot</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSlotForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_slot_id" name="slot_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_day_of_week" class="form-label">Day of Week</label>
                            <select class="form-select" id="edit_day_of_week" name="day_of_week" required>
                                <option value="monday">Monday</option>
                                <option value="tuesday">Tuesday</option>
                                <option value="wednesday">Wednesday</option>
                                <option value="thursday">Thursday</option>
                                <option value="friday">Friday</option>
                                <option value="saturday">Saturday</option>
                                <option value="sunday">Sunday</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_duration_minutes" class="form-label">Duration (minutes)</label>
                            <select class="form-select" id="edit_duration_minutes" name="duration_minutes" required>
                                <option value="15">15 minutes</option>
                                <option value="30">30 minutes</option>
                                <option value="45">45 minutes</option>
                                <option value="60">60 minutes</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_start_time" class="form-label">Start Time</label>
                            <input type="time" class="form-control" id="edit_start_time" name="start_time" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_end_time" class="form-label">End Time</label>
                            <input type="time" class="form-control" id="edit_end_time" name="end_time" readonly>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_max_appointments" class="form-label">Max Appointments</label>
                            <input type="number" class="form-control" id="edit_max_appointments" name="max_appointments" 
                                   min="1" max="10" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="edit_is_available" name="is_available">
                                <label class="form-check-label" for="edit_is_available">
                                    Available for booking
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Time Slot</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Calculate end time based on start time and duration
    function calculateEndTime(startTime, duration) {
        if (!startTime || !duration) return '';
        
        const [hours, minutes] = startTime.split(':').map(Number);
        const startMinutes = hours * 60 + minutes;
        const endMinutes = startMinutes + parseInt(duration);
        
        const endHours = Math.floor(endMinutes / 60);
        const endMins = endMinutes % 60;
        
        return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
    }
    
    // Add slot form handlers
    document.getElementById('start_time').addEventListener('change', function() {
        const duration = document.getElementById('duration_minutes').value;
        document.getElementById('end_time').value = calculateEndTime(this.value, duration);
    });
    
    document.getElementById('duration_minutes').addEventListener('change', function() {
        const startTime = document.getElementById('start_time').value;
        document.getElementById('end_time').value = calculateEndTime(startTime, this.value);
    });
    
    // Edit slot form handlers
    document.getElementById('edit_start_time').addEventListener('change', function() {
        const duration = document.getElementById('edit_duration_minutes').value;
        document.getElementById('edit_end_time').value = calculateEndTime(this.value, duration);
    });
    
    document.getElementById('edit_duration_minutes').addEventListener('change', function() {
        const startTime = document.getElementById('edit_start_time').value;
        document.getElementById('edit_end_time').value = calculateEndTime(startTime, this.value);
    });
    
    // Edit slot function
    function editSlot(slot) {
        document.getElementById('edit_slot_id').value = slot.id;
        document.getElementById('edit_day_of_week').value = slot.day_of_week;
        document.getElementById('edit_duration_minutes').value = slot.duration_minutes;
        document.getElementById('edit_start_time').value = slot.start_time;
        document.getElementById('edit_end_time').value = slot.end_time;
        document.getElementById('edit_max_appointments').value = slot.max_appointments;
        document.getElementById('edit_is_available').checked = slot.is_available;
        
        const modal = new bootstrap.Modal(document.getElementById('editSlotModal'));
        modal.show();
    }
    
    // Toggle slot availability
    function toggleSlot(slotId, isAvailable) {
        if (confirm('Are you sure you want to ' + (isAvailable === 'true' ? 'enable' : 'disable') + ' this time slot?')) {
            // Implementation for toggling slot availability
            console.log('Toggle slot', slotId, isAvailable);
        }
    }
    
    // Delete slot
    function deleteSlot(slotId) {
        if (confirm('Are you sure you want to delete this time slot? This action cannot be undone.')) {
            // Implementation for deleting slot
            console.log('Delete slot', slotId);
        }
    }
</script>
<?= $this->endSection() ?>

<?php
// Helper function for time formatting
function formatTime($time) {
    $parts = explode(':', $time);
    $hour = (int)$parts[0];
    $minute = $parts[1];
    $ampm = $hour >= 12 ? 'PM' : 'AM';
    $displayHour = $hour % 12 ?: 12;
    return "{$displayHour}:{$minute} {$ampm}";
}
?>
