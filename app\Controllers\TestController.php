<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AdminModel;

class TestController extends BaseController
{
    public function testAdminLogin()
    {
        $adminModel = new AdminModel();
        
        // Find admin by email
        $admin = $adminModel->where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            echo "Admin not found in database";
            return;
        }
        
        echo "Admin found: " . $admin['name'] . "<br>";
        echo "Email: " . $admin['email'] . "<br>";
        echo "Role: " . $admin['role'] . "<br>";
        echo "Is Active: " . ($admin['is_active'] ? 'Yes' : 'No') . "<br>";
        
        // Test password verification
        $testPassword = 'admin123';
        $isValid = password_verify($testPassword, $admin['password']);
        
        echo "Password verification for 'admin123': " . ($isValid ? 'VALID' : 'INVALID') . "<br>";
        
        // Test the model method
        $isValidModel = $adminModel->verifyPassword($testPassword, $admin['password']);
        echo "Model password verification: " . ($isValidModel ? 'VALID' : 'INVALID') . "<br>";
    }
}
